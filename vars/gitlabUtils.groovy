import groovy.transform.Field

@Field String gitHostname = 'gitlab.i.kefir.games'
@Field String gitHostnameInternal = 'gitlab.int.i.kefir.games'
@Field String gitHostnameDeprecated = 'gitlab.kefirgames.ru'
@Field String sharedLibraryPath = 'devops/jenkins-shared-libraries'
@Field String credentialsId = 'jenkins_gitlab_ssh_key'
@Field String readTokenCredentialsId = 'jenkins-download-raw-file-token'
@Field String version = '13.12'

String getGitSshUrl() {
    return "git@${this.gitHostname}"
}

String getGitSshUrlInternal() {
    return "git@${this.gitHostnameInternal}"
}

String getGitSshUrlDeprecated() {
    return "git@${this.gitHostnameDeprecated}"
}

String getBranch() {
    return env?.gitlabBranch ?: null
}

String getRepoSshUrl() {
    return env?.gitlabSourceRepoSshUrl ?: null
}

String *********************() {
    return env?.gitlabSourceRepoSshUrl ? env?.gitlabSourceRepoSshUrl?.replaceFirst("^${this.gitSshUrl}", this.gitSshUrlInternal) : null
}

String getRepoSshUrlDeprecated() {
    return env?.gitlabSourceRepoSshUrl ? env?.gitlabSourceRepoSshUrl?.replaceFirst("^${this.gitSshUrl}", this.gitSshUrlDeprecated) : null
}

Boolean update(Map args) {
    this.pipelineName = args?.name ?: env.JOB_NAME

    if ( env.gitlabBranch ) {
        updateGitlabCommitStatus(name: this.pipelineName, state: args.state)
    }
}

Boolean downloadFile(String PROJECT_ID, String PATH, String BRANCH) {
    Map tempFile = [
      label: 'Create tmp file for raw git file',
      script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"',
      returnStdout: true
    ]

    tempFilename = sh(tempFile).trim()
    String URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/files/${PATH.replace('/', '%2F')}/?ref=${scmUtils.stripGitRefspecPrefixes(BRANCH)}"

    withCredentials([string(credentialsId: "${this.readTokenCredentialsId}", variable: 'TOKEN')]) {
        sh("""
            curl --insecure --header "Private-Token: "\$TOKEN"" "${URL}" > ${tempFilename}
           """)
        output = readJSON(file: tempFilename)
        output_enc = sh(script: "echo ${output.content} | base64 --decode", returnStdout: true)
        writeFile(file: output.file_name, text: output_enc)
        sh("rm -f ${tempFilename}")
    }
}

Boolean downloadTreeFile(String PROJECT_ID, String PATH, String BRANCH) {
    Map tempFile = [
      label: 'Create tmp file for raw git file',
      script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"',
      returnStdout: true
    ]

    temp_tree_Filename = sh(tempFile).trim()
    // Restriction: 100 files as per_page=100 (use downloadRepoFolderAsArchive instead)
    String API_TREE_URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/tree?ref=${scmUtils.stripGitRefspecPrefixes(BRANCH)}&path=${PATH}&recursive=true&per_page=100"

    withCredentials([string(credentialsId: "${this.readTokenCredentialsId}", variable: 'TOKEN')]) {
        sh("""
            curl --insecure --header "Private-Token: "\$TOKEN"" "${API_TREE_URL}" > ${temp_tree_Filename}
        """)
        output_tree = readJSON(file: temp_tree_Filename)
        output_tree.each{elem ->
            if(elem['type'] != 'tree'){
                tempFilename = sh(tempFile).trim()
                String API_FILE_URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/files/${elem['path'].replace('/', '%2F')}/?ref=${scmUtils.stripGitRefspecPrefixes(BRANCH)}"
                withCredentials([string(credentialsId: "${this.readTokenCredentialsId}", variable: 'TOKEN')]) {
                        sh("""
                            curl --insecure --header "Private-Token: "\$TOKEN"" "${API_FILE_URL}" > ${tempFilename}
                        """)
                }
                output = readJSON(file: tempFilename)
                data_dir = sh(returnStdout: true, script:""" echo "${elem['path']}" | sed  -e "s/${output.file_name}//g" """)
                output_enc = sh(script: "echo ${output.content} | base64 --decode", returnStdout: true)
                writeFile(file: output.file_name, text: output_enc)
                sh("mkdir -p ${data_dir}")
                sh("mv ${output.file_name} ${data_dir}")
                sh("rm -f ${tempFilename}")
            }
        }
    }
    sh("rm -f ${temp_tree_Filename}")
}

Boolean downloadRepoFolderAsArchive(String PROJECT_ID, String COMMIT_SHA, String REPO_RELATIVE_PATH) {
    String tempFilename = sh(script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.tar"', returnStdout: true).trim()

    String API_TREE_URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/archive.tar?sha=${COMMIT_SHA}&path=${REPO_RELATIVE_PATH}"

    makeCurlRequest(API_TREE_URL, tempFilename)

    sh("tar -xf ${tempFilename} --strip-components=1")
    sh("rm -rf ${tempFilename}")
}

String getBranchLastCommitSha(String PROJECT_ID, String BRANCH, String COMMIT_TYPE = 'full') {
    String tempFilename = sh(script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"',
                             returnStdout: true).trim()

    String COMMIT_SHA = ''
    String API_TREE_URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/branches/${BRANCH.replace('/', '%2F')}"

    makeCurlRequest(API_TREE_URL, tempFilename)
    output = readJSON(file: tempFilename)

    if (COMMIT_TYPE == 'full') {
        COMMIT_SHA = output['commit']['id']
    } else if (COMMIT_TYPE == 'short') {
        COMMIT_SHA = output['commit']['short_id']
    }

    sh("rm -f ${tempFilename}")

    return COMMIT_SHA
}

String getCommitShaByBranchOrTag(String PROJECT_ID, String BRANCH_OR_TAG, String COMMIT_TYPE = 'full') {
    String tempFilename = sh(script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"', returnStdout: true).trim()

    String COMMIT_SHA = ''
    String API_TREE_URL = "https://${this.gitHostname}/api/v4/projects/${PROJECT_ID}/repository/commits/${BRANCH_OR_TAG.replace('/', '%2F')}"

    makeCurlRequest(API_TREE_URL, tempFilename)
    output = readJSON(file: tempFilename)

    if (COMMIT_TYPE == 'full') {
        COMMIT_SHA = output['id']
    } else if (COMMIT_TYPE == 'short') {
        COMMIT_SHA = output['short_id']
    }

    sh("rm -f ${tempFilename}")

    return COMMIT_SHA
}

Boolean makeCurlRequest(String url, String tempFilename) {
    try {
        withCredentials([string(credentialsId: "${this.readTokenCredentialsId}", variable: 'TOKEN')]) {
            sh("""
                curl --insecure --header "Private-Token: "\$TOKEN"" --url "${url}" > ${tempFilename}
            """)
        }
    }
    catch (err) {
        error("Curl request failed. URL : ${url}")
    }
}
