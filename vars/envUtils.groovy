String getDirectorySeparator() {
    return isUnix() ? '/' : '\\'
}

Boolean init(Map args) {
    if (!isUnix()) {
        return false
    }

    if (!args?.skip || (args?.skip && args?.skip?.size() == 0) || !args?.skip?.contains('KUBECONFIG')) {
        env.KUBECONFIG = sh(label: 'Setting up KUBECONFIG environment variable', script: 'mktemp "$WORKSPACE/.kubeconfig.XXXXXXXXXX"', returnStdout: true).trim()
    }

    if (!args?.skip || (args?.skip && args?.skip?.size() == 0) || !args?.skip?.contains('CLOUDSDK_CONFIG')) {
        env.CLOUDSDK_CONFIG = sh(label: 'Setting up CLOUDSDK_CONFIG environment variable', script: 'mktemp -d "$WORKSPACE/.gcloud.XXXXXXXXXX"', returnStdout: true).trim()
        sh(label: 'Configuring Google Cloud SDK workspace parameters', script: '''
gcloud config set core/disable_prompts True
gcloud config set survey/disable_prompts True
''')
    }
}

Boolean deinit() {
    if (env?.KUBECONFIG) {
        fileOperations([
            fileDeleteOperation(includes: env.KUBECONFIG.substring(env.WORKSPACE.length() + 1))
        ])
        env.KUBECONFIG = ''
    }

    if (env?.CLOUDSDK_CONFIG) {
        dir(env.CLOUDSDK_CONFIG) {
            deleteDir()
        }
        env.CLOUDSDK_CONFIG = ''
    }
}

Boolean call(Map args) {
    return this.init(args)
}
