Boolean call(Map args) {
    this.repositoryName = args?.repositoryName ?: (binding.hasVariable('repositoryName') ? this.repositoryName : 'kefir')
    this.repositoryAddress = args?.repositoryAddress ?: (binding.hasVariable('repositoryAddress') ? this.repositoryAddress : 'gs://kefir-helm/')
    this.credentialsId = args?.repositoryCredentials ?: (binding.hasVariable('credentialsId') ? this.credentialsId : "${gcpUtils.infraProject}.json")

    args.each { key, value ->
        this."$key" = value
    }

    return true
}

String searchFile(List directories, String file, Boolean capitalizeDir = true) {
    if (!binding.hasVariable('locationCache')) {
        this.locationCache = [:]
    }

    String mapIndex = pwd() + directories.join(':') + file + capitalizeDir
    if (this.locationCache?."$mapIndex") {
        return this.locationCache?."$mapIndex"
    }

    for (String dir in directories) {
        String checkPath = "${dir}/${file}"
        if (fileExists(checkPath)) {
            this.locationCache."$mapIndex" = checkPath
            return this.locationCache."$mapIndex"
        }

        if (capitalizeDir) {
            checkPath = dir.capitalize() + "/${file}"
            if (fileExists(checkPath)) {
                this.locationCache."$mapIndex" = checkPath
                return this.locationCache."$mapIndex"
            }
        }
    }

    return null
}

List getSupposedHelmDirectories() {
    return ['helm', 'chart']
}

String getHelmChartPath() {
    return searchFile(this.supposedHelmDirectories, 'Chart.yaml')
}

String getHelmValuesPath() {
    return searchFile(this.supposedHelmDirectories, 'values.yaml')
}

String getHelmDirectoryPath() {
    return this.helmChartPath.split(envUtils.directorySeparator).dropRight(1).join(envUtils.directorySeparator)
}

Map variables(Map args) {
    if (!args?.update && binding.hasVariable('imageUri')) {
        return this
    }

    Map vars = [:]

    if (args?.appVersion) {
        vars.appVersion = args?.appVersion
    }

    if (this.helmChartPath && !vars?.appVersion) {
        Map helmChart = readYaml(file: this.helmChartPath)
        vars.appVersion = helmChart?.appVersion?.trim()
    }

    if (args?.scmVars?.GIT_TAG_REFSPEC && !vars?.appVersion) {
        vars.appVersion = args?.scmVars?.GIT_TAG_REFSPEC
    }

    if (args?.imageTag) {
        vars.imageTag = args?.imageTag
    } else {
        vars.buildVersionId = vars?.appVersion ? "${vars.appVersion}-${args?.scmVars?.GIT_COMMIT_SHORT_SHA}-${env.BUILD_NUMBER}" : "${args?.scmVars?.GIT_BRANCH_REFSPEC_SLUG}-${args?.scmVars?.GIT_COMMIT_SHORT_SHA}-${env.BUILD_NUMBER}"
        vars.buildVersionStable = vars?.appVersion ? vars?.appVersion + (args?.params?.IMAGE_TAG_LATEST_BRANCH == args?.scmVars?.GIT_BRANCH_REFSPEC ? '' : "-${args?.scmVars?.GIT_BRANCH_REFSPEC_SLUG}") : args?.scmVars?.GIT_BRANCH_REFSPEC_SLUG
        vars.imageTag = args?.params?.IMAGE_TAG_STABLE ? vars.buildVersionStable : vars.buildVersionId
    }

    if (args?.registryName && args?.repositoryName) {
        vars.imageRepository = [
            args?.registryName,
            args?.repositoryName,
            args?.imageName ?: args?.scmVars?.GIT_URL?.tokenize('/')?.last()
        ]
    }

    String repositoryPath = args?.repositoryPath ?: 'image.repository'
    if (args?.params?.binding?.hasVariable('HELM_RELEASE') && !vars?.imageRepository) {
        Map helmValues = this.releaseInfo(params: args.params, cmd: 'values -a')
        vars.imageRepository = repositoryPath.split(/\./).inject(helmValues) { obj, prop -> obj?."$prop" }?.trim()?.split('/')
    }
    if (!vars?.imageRepository || vars?.imageRepository?.size() < 3) {
        if (this.helmValuesPath) {
            Map helmValues = readYaml(file: this.helmValuesPath)
            vars.imageRepository = repositoryPath.split(/\./).inject(helmValues) { obj, prop -> obj?."$prop" }?.trim()?.split('/')
        } else {
            vars.imageRepository = [
                args?.registryName ?: 'eu.gcr.io',
                args?.repositoryName ?: gcpUtils.infraProject,
                args?.imageName ?: args?.scmVars?.GIT_URL?.tokenize('/')?.last()
            ]
        }
    }

    vars.registryName = args?.registryName ?: vars.imageRepository.first()
    vars.imageName = args?.imageName ?: vars.imageRepository.last()

    if ( !vars.imageName || vars.imageName == vars.registryName ) {
        error('Unable to determine image name from Helm values')
    }

    vars.imageUri = vars.imageRepository.join('/')

    this.call(vars)
    return this
}

Map releaseInfo(Map args) {
    if (!isUnix()) {
        return [:]
    }

    if (!binding.hasVariable('releaseInfoCache')) {
        this.releaseInfoCache = [:]
    }

    String cmd = (args?.cmd ?: 'values -a') + " \"${args?.params?.HELM_RELEASE}\" --namespace \"${args?.params?.K8S_NAMESPACE}\""
    if (this.releaseInfoCache?."$cmd" && !args?.update) {
        return this.releaseInfoCache."$cmd"
    }

    try {
        withCredentials([[$class: 'FileBinding', credentialsId: "${args?.params?.GCP_PROJECT}.json", variable: 'JSON_KEY']]) {
            String releaseInfo = sh(label: 'Attempting to retrieve Helm release data', returnStdout: true, script: """
cleanup(){
    gcloud auth revoke --format=none || true
}
trap cleanup EXIT

gcloud auth activate-service-account --key-file="\$JSON_KEY" --format=none
export GOOGLE_APPLICATION_CREDENTIALS="\$JSON_KEY"
gcloud container clusters get-credentials "${args?.params?.K8S_CLUSTER}" --region="\$(gcloud container clusters list --filter="name:${args?.params?.K8S_CLUSTER}" --project="${args?.params?.GCP_PROJECT}" --format="value(location)")" --project="${args?.params?.GCP_PROJECT}" --format=none

helm get ${cmd} -o yaml
""").trim()
            this.releaseInfoCache."$cmd" = readYaml(text: releaseInfo)
        }
    } catch (error) {
        echo(error.message)
    }
    return this.releaseInfoCache."$cmd" ?: [:]
}

Boolean installDependencies(Map args) {
    if ( !args?.params?.DEPENDENCIES ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    if ( isUnix() ) {
        return sh(label: 'Installing external dependencies', script: '''
mkdir -p ~/bin

echo "Installing Helm"
curl https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 | USE_SUDO=false HELM_INSTALL_DIR="$HOME/bin" bash

echo "Installing Helm-GCS plugin"
helm plugin install https://github.com/hayorov/helm-gcs.git || true

echo "Installing Kubectl"
KK_LOCAL=""
if [ -f "$HOME/bin/kubectl_stable.txt" ]; then
    KK_LOCAL=$(cat "$HOME/bin/kubectl_stable.txt")
fi
KK_STABLE="$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)"

if [ "n$KK_LOCAL" != "n$KK_STABLE" ]; then
    echo "Downloading Kubectl v.$KK_STABLE"
    curl -L -o "$HOME/bin/kubectl" "https://storage.googleapis.com/kubernetes-release/release/$KK_STABLE/bin/linux/amd64/kubectl"
    chmod +x "$HOME/bin/kubectl"
    echo -n "$KK_STABLE" > "$HOME/bin/kubectl_stable.txt"
fi

echo "Installing jq 1.6"
if [ ! -f "$HOME/bin/jq" ]; then
    curl -L -o "$HOME/bin/jq" https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64
    chmod +x "$HOME/bin/jq"
fi
''')
    }

    // Installing build dependencies on Windows not implemented yet
    return false
}

// codenarc-disable ImplicitReturnStatement
Boolean deliverChart(Map args) {
    // codenarc-enable
    if ( !args?.params?.DELIVER ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    if ( !isUnix() ) {
        return false
    }

    this.call(args)

    withEnv([
        "HELM_REPO_NAME=${this.repositoryName}",
        "HELM_REPO_ADDR=${this.repositoryAddress}",
        "HELM_CHART_DIR=${this.helmDirectoryPath}"
    ]) {
        withCredentials([[$class: 'FileBinding', credentialsId: this.credentialsId, variable: 'JSON_KEY']]) {
            return sh(label: 'Packaging (delivering) Helm Chart', script: '''
export GOOGLE_APPLICATION_CREDENTIALS="$JSON_KEY"

echo "Configuring Helm repositories"
helm repo add "$HELM_REPO_NAME" "$HELM_REPO_ADDR" || true

echo "Packaging (delivering) Helm Chart"
pkg_dir=$(mktemp -d ./helm_package.XXXXXXXXXX)
rm -rfv "$HELM_CHART_DIR/tmpcharts"
helm dependency update "$HELM_CHART_DIR"
helm package "$HELM_CHART_DIR" -u -d "$pkg_dir"
helm gcs push "$pkg_dir/"* "$HELM_REPO_NAME" --force
rm -rf "$pkg_dir"
''')
        }
    }
}

Boolean deployChart(Map args) {
    if ( !args?.params?.DEPLOY ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    if ( !isUnix() ) {
        return false
    }

    this.call(args)

    String exposedArgs = ''
    String upgradeArgs = ''
    String helmValuesPathTmp
    String releaseValuesPath
    String apiResource = args?.apiResource ?: 'deployment'
    String apiObject = args?.apiObject ?: args?.params?.HELM_RELEASE
    List<Map> ensureAdditional = args?.ensureAdditional ?: []
    String ensureHelmValue = args?.ensureHelmValue ?: ''

    if (args?.params?.binding?.hasVariable('DEPLOY_WAIT') && args?.params?.DEPLOY_WAIT) {
        exposedArgs += ' --wait'
    }

    if (args?.params?.binding?.hasVariable('DEPLOY_WAIT_TIMEOUT') && args?.params?.DEPLOY_WAIT_TIMEOUT) {
        exposedArgs += " --timeout=\"${args?.params?.DEPLOY_WAIT_TIMEOUT}s\""
    }

    if (args?.params?.binding?.hasVariable('HELM_CHART_VERSION')) {
        upgradeArgs += " --version \"${args?.params?.HELM_CHART_VERSION}\""
    }

    if (ensureHelmValue && args?.params?.binding?.hasVariable('FORCE_RESTART') && args?.params?.FORCE_RESTART) {
        upgradeArgs += " --set-string \"${ensureHelmValue}=${currentBuild.startTimeInMillis}\""
    }

    if ('Reuse' == args?.params?.HELM_VALUES &&
        !(args?.params?.binding?.hasVariable('HELM_MERGE_VALUES') && args?.params?.HELM_MERGE_VALUES) // Update from one of the library's previous versions
    ) {
        upgradeArgs += ' --reuse-values'
    }

    Map releaseValuesPathShellArgs = [
        label: 'Creating storage for Helm release values',
        script: 'mktemp -u "$WORKSPACE/.helm-release-values.XXXXXXXXXX.yaml"',
        returnStdout: true
    ]
    if ('Merge' == args?.params?.HELM_VALUES ||
        (args?.params?.binding?.hasVariable('HELM_MERGE_VALUES') && args?.params?.HELM_MERGE_VALUES)
    ) {
        Map releaseValues = this.releaseInfo(params: args.params, cmd: 'values')
        if (releaseValues) {
            releaseValuesPath = sh(releaseValuesPathShellArgs).trim()
            writeYaml(file: releaseValuesPath, data: releaseValues)
            upgradeArgs += " --reset-values -f \"${releaseValuesPath}\""
        } else {
            upgradeArgs += ' --reuse-values'
        }
    }

    if (('MergeAll' == args?.params?.HELM_VALUES ||
        (args?.params?.binding?.hasVariable('HELM_MERGE_VALUES') && args?.params?.HELM_MERGE_VALUES)) &&
        this.helmValuesPath
    ) {
        Map releaseValues = this.releaseInfo(params: args.params, cmd: 'values')
        if (releaseValues) {
            releaseValuesPath = sh(releaseValuesPathShellArgs).trim()
            writeYaml(file: releaseValuesPath, data: releaseValues)

            helmValuesPathTmp = sh(label: 'Creating temporary storage for purified Helm values', script: 'mktemp -u "$WORKSPACE/.helm-values.XXXXXXXXXX.yaml"', returnStdout: true).trim()
            // sh(label: 'Purifying Helm values', script: "grep -vP '(^\\s*#)|((:\\s*(\"\")|(\\[\\s*\\])|({\\s*})\\s*))\$' '${this.helmValuesPath}' | sed ':a;N;\$!ba;s/\\n\\S\\+:\\n\$//g' > '${helmValuesPathTmp}'") // regexp madness
            String purifier = """
cat '${this.helmValuesPath}' | \
python3 -c 'import sys,yaml,json; json.dump(yaml.safe_load(sys.stdin), sys.stdout)' | \
jq 'walk(
    if "object" == type then
        with_entries(select(
            .value != null and .value != "" and .value != {} and .value != []
        ))
    else .
end)' | \
python3 -c 'import sys,yaml,json; print(yaml.dump(json.loads(sys.stdin.read()), default_flow_style=False), end="")' \
> '${helmValuesPathTmp}'
"""
            sh(label: 'Purifying Helm values', script: purifier)

            upgradeArgs += " --reset-values -f \"${helmValuesPathTmp}\" -f \"${releaseValuesPath}\""
        } else {
            upgradeArgs += ' --reuse-values'
        }
    }

    if (args?.values) {
        List valuesList = args.values
        if (valuesList && valuesList.size() > 0) {
            valuesList.each {
                upgradeArgs += " -f \"$it\""
            }
        }
    }

    if (args?.params?.binding?.hasVariable('IMAGE_PULL_POLICY') && args?.params?.IMAGE_PULL_POLICY) {
        String imagePullPolicyPath = args?.imagePullPolicyPath ?: 'image.pullPolicy'
        upgradeArgs += " --set \"$imagePullPolicyPath=${args?.params?.IMAGE_PULL_POLICY}\""
    }

    if (binding.hasVariable('appVersion') && binding.hasVariable('imageTag')) {
        String imageTagPath = args?.imageTagPath ?: 'image.tag'
        if (this.appVersion == this.imageTag && !args?.imageTagPath) {
            upgradeArgs += " --set \"$imageTagPath=\""
        } else {
            upgradeArgs += " --set \"$imageTagPath=${this.imageTag}\""
        }
    }

    if (args?.set) {
        Map argsMap = args.set
        if (argsMap && argsMap.size() > 0) {
            argsMap.each { k, v ->
                if (v instanceof CharSequence) {
                    upgradeArgs += ' --set-string '
                } else {
                    upgradeArgs += ' --set '
                }
                upgradeArgs += "\"$k=$v\""
            }
        }
    }

    withCredentials([[$class: 'FileBinding', credentialsId: "${args?.params?.GCP_PROJECT}.json", variable: 'JSON_KEY']]) {
        sh(label: 'Upgrading deployed Helm Chart', script: """#!/bin/bash
set -x
cleanup(){
    echo "Cleaning up"
    gcloud auth revoke || true
}
trap cleanup EXIT

echo "Authenticating against GCP"
gcloud auth activate-service-account --key-file="\$JSON_KEY"
export GOOGLE_APPLICATION_CREDENTIALS="\$JSON_KEY"
gcloud container clusters get-credentials "${args?.params?.K8S_CLUSTER}" --region="\$(gcloud container clusters list --filter="name:${args?.params?.K8S_CLUSTER}" --project="${args?.params?.GCP_PROJECT}" --format="value(location)")" --project="${args?.params?.GCP_PROJECT}"

echo "Configuring Helm repositories"
helm repo add "${this.repositoryName}" "${this.repositoryAddress}" || true

if [[ -d "${this.helmDirectoryPath}" ]]; then
    if [[ -z "\$(ls -A "${this.helmDirectoryPath}/charts")" ]]; then
        echo "Updating Helm Chart dependencies"
        helm dependency update "${this.helmDirectoryPath}"
    fi
else
    echo "Updating Helm repositories"
    helm repo update
fi

echo "Retrieving Helm Chart status before upgrade"
helm status "${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --show-desc

echo "Upgrading deployed Helm Chart"
set +e
(
set -e
helm upgrade "${args?.params?.HELM_RELEASE}" "${this.helmDirectoryPath}" --namespace "${args?.params?.K8S_NAMESPACE}" --debug --render-subchart-notes${exposedArgs}${upgradeArgs} > /dev/null
helm status "${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --show-desc
if [[ "true" == "${args?.params?.FORCE_RESTART}" && "" == "${ensureHelmValue}" ]]; then
    kubectl rollout restart "${apiResource}/${apiObject}" --namespace "${args?.params?.K8S_NAMESPACE}"
    if [[ "true" == "${args?.params?.DEPLOY_WAIT}" ]]; then
        kubectl rollout status "${apiResource}/${apiObject}" --timeout="${args?.params?.DEPLOY_WAIT_TIMEOUT}s" --namespace "${args?.params?.K8S_NAMESPACE}"
    fi
fi
)
retval=\$?
set -e

if [[ "\$retval" != 0 ]]; then
    echo "Something failed, retrieving logs"
    kubectl logs -l "app.kubernetes.io/instance=${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --all-containers --prefix --tail 250

    if [[ "true" == "${args?.params?.DEPLOY_ATOMIC}" ]]; then
        helm rollback "${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --debug${exposedArgs} > /dev/null
        helm status "${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --show-desc
    fi

    exit \$retval
fi
""")

        for (Map add in ensureAdditional) {
            sh(label: 'Ensuring additional API', script: """#!/bin/bash
set -x
cleanup(){
    echo "Cleaning up"
    gcloud auth revoke || true
}
trap cleanup EXIT

echo "Authenticating against GCP"
gcloud auth activate-service-account --key-file="\$JSON_KEY"
export GOOGLE_APPLICATION_CREDENTIALS="\$JSON_KEY"
gcloud container clusters get-credentials "${args?.params?.K8S_CLUSTER}" --region="\$(gcloud container clusters list --filter="name:${args?.params?.K8S_CLUSTER}" --project="${args?.params?.GCP_PROJECT}" --format="value(location)")" --project="${args?.params?.GCP_PROJECT}"

echo "Upgrading deployed Helm Chart"
set +e
(
set -e
if [[ "true" == "${args?.params?.FORCE_RESTART}" ]]; then
    kubectl rollout restart "${add.apiResource}/${add.apiObject}" --namespace "${args?.params?.K8S_NAMESPACE}"
    if [[ "true" == "${args?.params?.DEPLOY_WAIT}" ]]; then
        kubectl rollout status "${add.apiResource}/${add.apiObject}" --timeout="${args?.params?.DEPLOY_WAIT_TIMEOUT}s" --namespace "${args?.params?.K8S_NAMESPACE}"
    fi
fi
)
retval=\$?
set -e

if [[ "\$retval" != 0 ]]; then
    echo "Something failed, retrieving logs"
    kubectl logs -l "app.kubernetes.io/instance=${args?.params?.HELM_RELEASE}" --namespace "${args?.params?.K8S_NAMESPACE}" --all-containers --prefix --tail 250

    exit \$retval
fi
""")
        }
    }

    if (helmValuesPathTmp || releaseValuesPath) {
        Integer wsLen = env.WORKSPACE.length() + 1
        List ops = []
        if (helmValuesPathTmp) {
            ops.add(fileDeleteOperation(includes: helmValuesPathTmp.substring(wsLen)))
        }
        if (releaseValuesPath) {
            ops.add(fileDeleteOperation(includes: releaseValuesPath.substring(wsLen)))
        }
        fileOperations(ops)
    }

    return true
}
