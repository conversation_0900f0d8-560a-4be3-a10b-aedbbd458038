def call(String UNITY_PROJECT_PATH, String BUILD_STATE) {
    String MANIFEST_SOURCE = "${UNITY_PROJECT_PATH}\\AssemblyStates\\Manifest\\${BUILD_STATE}.json"
    String RSP_SOURCE = "${UNITY_PROJECT_PATH}\\AssemblyStates\\Rsp\\${BUILD_STATE}.rsp"

    String MANIFEST_DESTINATION = "${UNITY_PROJECT_PATH}\\Packages\\manifest.json"
    String RSP_DESTINATION = "${UNITY_PROJECT_PATH}\\Assets\\csc.rsp"

    fileOperations([
        fileDeleteOperation(includes: RSP_DESTINATION),
        fileRenameOperation(source: RSP_SOURCE, destination: RSP_DESTINATION)
    ])

    if (fileExists(MANIFEST_SOURCE)) {
        fileOperations([
            fileDeleteOperation(includes: MANIFEST_DESTINATION),
            fileRenameOperation(source: MANIFEST_SOURCE, destination: MANIFEST_DESTINATION)
        ])
    }
}
