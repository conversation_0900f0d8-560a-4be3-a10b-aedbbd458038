import hudson.Util
import groovy.transform.Field

@Field Integer maxMsgLen = 100

Boolean call(Map args) {
    if (args?.maxMsgLen) {
        this.maxMsgLen = args?.maxMsgLen
    }
}

String getChangeString() {
    String changeString = ''
    echo('Gathering SCM changes')
    def changeLogSets = currentBuild.changeSets
    for (int i = 0; i < changeLogSets.size(); i++) {
        def entries = changeLogSets[i].items
        for (int j = 0; j < entries.length; j++) {
            def entry = entries[j]
            truncated_msg = entry.msg.take(this.maxMsgLen)
            truncated_sha = entry.commitId.take(8)
            changeString += " • ${truncated_msg} [<mailto:${entry.authorEmail}|${entry.author}>] #<https://${gitlabUtils.gitHostname}/search?scope=commits&search=${entry.commitId}|${truncated_sha}>\n"
        }
    }

    return changeString
}

String formatHeader(Map args) {
    msg = "Job <${env.JOB_URL}|${env.JOB_NAME}> <${env.BUILD_URL}|${env.BUILD_DISPLAY_NAME}> ${args?.result}\n"
    if (args?.scmVars) {
        msg += "on branch: <${args?.scmVars?.GIT_URL}/-/tree/${args?.scmVars?.GIT_BRANCH_REFSPEC}|${args?.scmVars?.GIT_BRANCH_REFSPEC}>, commit: <${args?.scmVars?.GIT_URL}/-/commit/${args?.scmVars?.GIT_COMMIT}|${args?.scmVars?.GIT_COMMIT_SHORT_SHA}>"
        if (args?.params && args?.params?.binding?.hasVariable('SHARED_LIB_BRANCH')) {
            msg += ", shared library: <https://${gitlabUtils.gitHostname}/${gitlabUtils.sharedLibraryPath}/-/tree/${args?.params?.SHARED_LIB_BRANCH}|${args?.params?.SHARED_LIB_BRANCH}>"
        }
        msg += '\n'
    }

    String changes = changeString
    msg += changes ? "changes:\n\n${changes}\n" : ''
    return msg
}

String formatFooter() {
    String msg = ''
    String buildDurationString = Util.getTimeSpanString(currentBuild.duration)
    String timings = "at: _${dateUtils.currentDate}_ and took: _${buildDurationString}_"
    Integer causesSize = currentBuild?.buildCauses?.size()
    if (causesSize) {
        currentBuild.buildCauses.each {
            if (it?.shortDescription) {
                msg += (causesSize > 1 ? ' • ' : '') + it.shortDescription + (causesSize > 1 ? '\n' : " $timings")
            }
        }
        msg += causesSize > 1 ? timings : ''
    } else {
        msg += timings
    }
    return msg
}

Boolean success(Map args) {
    if (args?.params &&
        (args?.params?.binding?.hasVariable('SLACK_DISABLE') && args?.params?.SLACK_DISABLE) ||
        (args?.params?.binding?.hasVariable('SLACK_DISABLE_SUCCESS') && args?.params?.SLACK_DISABLE_SUCCESS)
    ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    this.call(args)

    String msg = this.formatHeader(
        result: 'successful',
        scmVars: args?.scmVars,
        params: args.params
    )

    if (args?.message) {
        msg += args?.message
    }

    if (args?.containerImages && args?.containerImages?.size() > 0) {
        args.containerImages.each {
            msg += "image: <https://${it.imageUri}:${it.imageTag}|${it.imageUri}:${it.imageTag}>\n"
        }
    }

    if (args?.params?.binding?.hasVariable('DEPLOYMENT') && (!args?.paramsFilter || args?.paramsFilter.contains('DEPLOYMENT'))) {
        msg += "deployment: ${args?.params?.DEPLOYMENT}\n"
    }

    if (args?.params) {
        msg += 'parameters: '
        List filter = args?.paramsFilter ?: ['BUILD', 'DELIVER', 'DEPLOY']
        args?.params?.binding?.variables?.each { key, value ->
            if (value == null || (filter.size() > 0 && !filter.contains(key))) {
                return
            }
            msg += "_${key}_=*$value* "
        }
        msg += '\n'
    }

    msg += formatFooter()

    return mattermostSend(
        channel: args?.channel ?: '#jenkins',
        color: 'good',
        message: msg
    )
}

Boolean shortStatus(Map args) {
    this.call(args)
    result = args?.status
    String msg = ''

    if (args?.message) {
        msg += args?.message
    }

    return mattermostSend(
        channel: args?.channel ?: '#jenkins',
        message: msg
    )
}

Boolean failure(Map args) {
    if (args?.params &&
        (args?.params?.binding?.hasVariable('SLACK_DISABLE') && args?.params?.SLACK_DISABLE) ||
        (args?.params?.binding?.hasVariable('SLACK_DISABLE_FAILURE') && args?.params?.SLACK_DISABLE_FAILURE)
    ) {
        return true
    }

    this.call(args)

    String msg = this.formatHeader(
        result: 'failed',
        scmVars: args?.scmVars,
        params: args?.params
    )

    msg += args?.error?.message ? "due to: *${args?.error?.message}*\n" : ''
    msg += formatFooter()

    return mattermostSend(
        channel: args?.channel ?: '#jenkins',
        color: 'danger',
        message: msg
    )
}
