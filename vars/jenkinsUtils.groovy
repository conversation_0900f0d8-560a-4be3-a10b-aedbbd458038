import org.jenkinsci.plugins.pipeline.modeldefinition.Utils

Boolean markStageSkipped() {
    echo("Skipping stage ${env.STAGE_NAME}")
    Utils.markStageSkippedForConditional(env.STAGE_NAME)
    return true
}

String getBuildOwner(build) {
    buildCauses = build.getBuildCauses()
    buildOwner = buildCauses.userName[0] ?: buildCauses.upstreamProject[0]
    return buildOwner
}

String getCleanBuildLog(build) {
    cleanBuildLog = ''
    for (line in build.getRawBuild().getLog().split('\n')) {
        cleanBuildLog += line.replaceAll(/ha:\/\/\/\/.*=/, '')
                             .replaceAll(/ha:\/\/\/\/.*\[0m/, '')
                             .replaceAll('\\[8m', '')
                             .replaceAll('\\[0m', '') + '\n'
    }
    return cleanBuildLog
}

String determineAgent() {
    def hour = new Date().hours
    def day = new Date().day

    if (day >= 1 && day <= 5 && hour >= 10 && hour < 19) {
        return 'gce'
    }
    else {
        return 'gce-extra'
    }
}
