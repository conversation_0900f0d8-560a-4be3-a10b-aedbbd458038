def call(Map config) {
    if (env.CHANGE_ID) {
        runValidatePipeline(config.validateConfig ?: [:])
    } else if (env.BRANCH_NAME == 'master') {
        runBuildPipeline(config.buildConfig ?: [:])
    } else {
        echo "Skipping pipeline for branch ${env.BRANCH_NAME}"
    }
}

private void runValidatePipeline(Map config) {
    if (!config) return
    validatePipeline(config)
}

private void runBuildPipeline(Map config) {
    if (!config) return
    buildPipeline(config)
}