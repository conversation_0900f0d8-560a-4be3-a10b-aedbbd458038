import org.kefir.services.DocBuilder
import org.kefir.services.NpmService

def call(Map config) {
    def npmService = new NpmService(this)
    def docBuilder = new DocBuilder(this)
    def publishResult = null

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        stages {
            stage('Публикация приложения') {
                when {
                    expression { config.app.enabled }
                }
                steps {
                    script {
                        publishResult = npmService.publishNpm([
                            verdaccioInstance: config.app.verdaccioInstance,
                            nodeJsTool: config.app.nodeJsTool
                        ])

                        if (!publishResult.success) {
                            error("Ошибка публикации пакета: ${publishResult.log}")
                        }

                        echo publishResult.log
                    }
                }
            }
            stage('Публикация документации в Confluence') {
                when {
                    expression { config.docs.enabled }
                }
                steps {
                    script {
                        docBuilder.publishDocs([
                            confluenceSpaceName: config.docs.confluenceSpaceName,
                            confluenceParentPageID: config.docs.confluenceParentPageID,
                            documentationDirectoryPath: config.docs.documentationDirectoryPath,
                            documentationLabel: config.docs.documentationLabel,
                            documentationTitle: config.docs.documentationTitle,
                            documentationHomePagePath: config.docs.documentationHomePagePath
                        ])
                    }
                }
            }
        }

        post {
            success {
                script {
                    if (config.app?.enabled && config.notifications?.enabled) {
                        mattermostUtils.packagePublished([
                            channel: config.notifications?.channel,
                            branch: env.BRANCH_NAME ?: 'master'
                        ])
                    }
                }
            }
            failure {
                script {
                    if (config.notifications?.enabled) {
                        mattermostUtils.buildFailed([
                            channel: config.notifications?.channel,
                            branch: env.BRANCH_NAME ?: 'master',
                            error: [message: publishResult?.error ?: 'Build failed']
                        ])
                    }
                }
            }
            unstable {
                script {
                    if (config.notifications?.enabled) {
                        mattermostUtils.sendBuildStatus([
                            channel: config.notifications?.channel,
                            branch: env.BRANCH_NAME ?: 'master',
                            status: 'unstable'
                        ])
                    }
                }
            }
        }
    }
}