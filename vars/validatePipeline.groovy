import org.kefir.services.CommentBuilder
import org.kefir.services.GitlabService
import org.kefir.validators.PackageJsonValidator
import org.kefir.validators.InitPyValidator

def call(Map config) {
    def packageJsonValidator = new PackageJsonValidator(this)
    def initPyValidator = new InitPyValidator(this)
    def gitlab = new GitlabService(this)
    def commentBuilder = new CommentBuilder(this)
    def validationResults = []

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        options { skipDefaultCheckout() }

        stages {
            stage('Проверка существования файла __init__.py') {
                when {
                    allOf {
                        expression { config.checks.initPy.enabled }
                    }
                }
                steps {
                    script {
                        def result = initPyValidator.checkFileExistence()
                        validationResults << result
                        if (!result.success) {
                            error("Проверка существования файла __init__.py не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка блока `bl_info` в файле __init__.py') {
                when {
                    allOf {
                        expression { config.checks.initPy.enabled }
                        expression { config.checks.initPy.blInfo }
                    }
                }
                steps {
                    script {
                        def result = initPyValidator.checkBlInfo()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка блока bl_info не пройдена: ${result.log}")
                        }
                    }
                }
            }

            stage('Проверка поля `name` в файле __init__.py') {
                when {
                    allOf {
                        expression { config.checks.initPy.enabled }
                        expression { config.checks.initPy.name }
                    }
                }
                steps {
                    script {
                        def result = initPyValidator.checkName()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля name не пройдена: ${result.log}")
                        }
                    }
                }
            }

            stage('Проверка поля `author` в файле __init__.py') {
                when {
                    allOf {
                        expression { config.checks.initPy.enabled }
                        expression { config.checks.initPy.author }
                    }
                }
                steps {
                    script {
                        def result = initPyValidator.checkAuthor()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля author не пройдена: ${result.log}")
                        }
                    }
                }
            }

            stage('Проверка поля `version` в файле __init__.py') {
                when {
                    allOf {
                        expression { config.checks.initPy.enabled }
                        expression { config.checks.initPy.version }
                    }
                }
                steps {
                    script {
                        def result = initPyValidator.checkVersion()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля version не пройдена: ${result.log}")
                        }
                    }
                }
            }

            stage('Проверка существования файла package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkFileExistence()
                        validationResults << result
                        if (!result.success) {
                            error("Проверка существования файла package.json не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `version` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.version }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkVersion()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля version не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `name` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.name }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkName()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля name не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `author` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.author }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkAuthor()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля author не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `displayName` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.displayName }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkDisplayName()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля displayName не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `repositoryUrl` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.repositoryUrl }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkRepositoryUrl()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля repositoryUrl не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `documentationUrl` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.documentationUrl }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkDocumentationUrl()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля documentationUrl не пройдена: ${result.log}")
                        }
                    }
                }
            }
            stage('Проверка поля `changelogUrl` в файле package.json') {
                when {
                    allOf {
                        expression { config.checks.packageJson.enabled }
                        expression { config.checks.packageJson.changelogUrl }
                    }
                }
                steps {
                    script {
                        def result = packageJsonValidator.checkChangelogUrl()
                        validationResults << result
                        if (!result.success) {
                            unstable("Проверка поля changelogUrl не пройдена: ${result.log}")
                        }
                    }
                }
            }
        }
        
        post {
            always {
                script {
                    String comment = commentBuilder.buildFullComment(validationResults)
                    gitlab.addMergeRequestComment(comment)
                }
            }
        }
    }
}