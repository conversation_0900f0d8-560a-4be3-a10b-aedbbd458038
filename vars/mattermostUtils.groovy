import org.kefir.services.MattermostNotificationService
import org.kefir.configs.MattermostConstants

/**
 * Utility functions for Mattermost notifications in Jenkins pipelines
 */

/**
 * Sends a notification about successful package publication
 * @param config Configuration map with the following options:
 *   - channel: Mattermost channel (default: '#builds-devops')
 *   - branch: Git branch to read package info from (default: env.BRANCH_NAME)
 *   - enabled: Whether notifications are enabled (default: true)
 * @return Boolean indicating success
 */
Boolean packagePublished(Map config = [:]) {
    try {
        def notificationService = new MattermostNotificationService(this)
        def result = notificationService.sendPackagePublishedNotification([
            channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
            branch: config.branch ?: env.BRANCH_NAME
        ])
        
        if (result.success) {
            echo "Package publication notification sent successfully"
            return true
        } else {
            echo "Failed to send package publication notification: ${result.log}"
            return false
        }
    } catch (Exception e) {
        echo "Error sending package publication notification: ${e.message}"
        return false
    }
}

/**
 * Sends a notification about build failure
 * @param config Configuration map with the following options:
 *   - channel: Mattermost channel (default: '#builds-devops')
 *   - branch: Git branch to read package info from (default: env.BRANCH_NAME)
 *   - error: Error information map with 'message' field
 *   - enabled: Whether notifications are enabled (default: true)
 * @return Boolean indicating success
 */
Boolean buildFailed(Map config = [:]) {
    try {
        def notificationService = new MattermostNotificationService(this)
        def result = notificationService.sendBuildFailedNotification([
            channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
            branch: config.branch ?: env.BRANCH_NAME
        ], config.error ?: [:])
        
        if (result.success) {
            echo "Build failure notification sent successfully"
            return true
        } else {
            echo "Failed to send build failure notification: ${result.log}"
            return false
        }
    } catch (Exception e) {
        echo "Error sending build failure notification: ${e.message}"
        return false
    }
}

/**
 * Sends a custom notification message
 * @param config Configuration map with the following options:
 *   - channel: Mattermost channel (default: '#builds-devops')
 *   - message: Custom message to send
 *   - color: Message color ('good', 'warning', 'danger', or hex color)
 *   - enabled: Whether notifications are enabled (default: true)
 * @return Boolean indicating success
 */
Boolean sendCustomMessage(Map config = [:]) {
    if (!config.message) {
        echo "No message provided for custom Mattermost notification"
        return false
    }
    
    try {
        def result = mattermostSend(
            channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
            color: config.color ?: MattermostConstants.COLOR_SUCCESS,
            message: config.message
        )
        
        echo "Custom Mattermost message sent successfully"
        return true
    } catch (Exception e) {
        echo "Error sending custom Mattermost message: ${e.message}"
        return false
    }
}

/**
 * Sends a notification with build status and package information
 * This is a comprehensive notification that includes build details, package info, and changelog
 * @param config Configuration map with the following options:
 *   - channel: Mattermost channel (default: '#jenkins')
 *   - branch: Git branch to read package info from (default: 'master')
 *   - status: Build status ('success', 'failure', 'unstable')
 *   - includeChangelog: Whether to include changelog (default: true)
 *   - enabled: Whether notifications are enabled (default: true)
 * @return Boolean indicating success
 */
Boolean sendBuildStatus(Map config = [:]) {
    String status = config.status ?: currentBuild.currentResult?.toLowerCase() ?: 'unknown'
    
    try {
        switch (status) {
            case 'success':
                return packagePublished(config)
            case 'failure':
                return buildFailed(config)
            case 'unstable':
                return sendCustomMessage([
                    channel: config.channel,
                    color: MattermostConstants.COLOR_WARNING,
                    message: String.format(MattermostConstants.BUILD_UNSTABLE_TITLE, env.JOB_NAME) + "\n\nBuild: [${env.BUILD_DISPLAY_NAME}](${env.BUILD_URL})",
                    enabled: config.enabled
                ])
            default:
                return sendCustomMessage([
                    channel: config.channel,
                    color: MattermostConstants.COLOR_INFO,
                    message: "ℹ️ **Build status: ${status}** for ${env.JOB_NAME}\n\nBuild: [${env.BUILD_DISPLAY_NAME}](${env.BUILD_URL})",
                    enabled: config.enabled
                ])
        }
    } catch (Exception e) {
        echo "Error sending build status notification: ${e.message}"
        return false
    }
}

/**
 * Helper method to check if Mattermost notifications should be sent
 * @param config Configuration map
 * @return Boolean indicating if notifications should be sent
 */
private static Boolean shouldSendNotification(Map config = [:]) {
    if (!config.enabled) {
        return false
    }
    
    return true
}
