import groovy.transform.Field

@Field String grafanaRulesBucketName = 'kefirinfra-prod-kube-prometheus-stack-gcs-grafana-alerting'
@Field String dashboardsBucketName = 'kefirinfra-prod-kube-prometheus-stack-gcs-grafana-dashboards'
@Field String prometheusTargetsBucketName = 'kefirinfra-prod-prometheus-targets'
@Field String credentialsId = 'kefirinfra'
@Field String grafanaAdminUsername = 'admin'
@Field String grafanaPasswordCredentialsId = 'kefirinfra-prod-grafana_adminpassword'
@Field String grafanaAlertingReloadURL = 'grafana.i.kefir.games/api/admin/provisioning/alerting/reload'

Boolean updateGrafanaAlerts(String rulesFile) {
    uploadGrafanaRulesToGCS(rulesFile)
    sleep(5)
    reloadGrafanaAlerting()
}

Boolean uploadDashboardsToGCS(String dashboardFile) {
    googleStorageUpload(
        credentialsId: "${this.credentialsId}",
        bucket: "gs://${dashboardsBucketName}",
        pattern: "${dashboardFile}"
    )
}

Boolean uploadGrafanaRulesToGCS(String rulesFile) {
    googleStorageUpload(
        credentialsId: "${this.credentialsId}",
        bucket: "gs://${grafanaRulesBucketName}",
        pattern: "${rulesFile}"
    )
}

Boolean reloadGrafanaAlerting() {
    withCredentials([
        string(credentialsId: "${this.grafanaPasswordCredentialsId}", variable: 'GRAFANA_PASS')
    ]) {
        retry(3) {
            response = sh(script: """
                curl --header "Content-Length: 0" -X POST https://${this.grafanaAdminUsername}:${GRAFANA_PASS}@${this.grafanaAlertingReloadURL} -w ' %{http_code}'
                """, returnStdout: true).trim()
            responseCode = response.tokenize(' ').last()
            print(response)
            if (responseCode != '200') {
                sleep(5)
                error("Grafana Alerting reload returned error: ${response}")
            }
        }
    }
}

// push prometheus config for dynamic btl roles
Boolean uploadPrometheusTargetsToGCS(String prometheusTargetFile) {
    if (!fileExists(prometheusTargetFile)) {
        error "file '${prometheusTargetFile}' not found!"
    }

    def content = readFile(prometheusTargetFile)
    if (!content.contains("targets")) {
        error "file '${prometheusTargetFile}' doesnt have targets!"
    }
    googleStorageUpload(
        credentialsId: "${this.credentialsId}",
        bucket: "gs://${prometheusTargetsBucketName}",
        pattern: "${prometheusTargetFile}"
    )
}

Boolean clearPrometheusTargetsInGCS(String prometheusTargetFile) {
    // clean json to remove http_sd_config target correctly
    sh ("echo [] > ${prometheusTargetFile}")
    googleStorageUpload(
        credentialsId: "${this.credentialsId}",
        bucket: "gs://${prometheusTargetsBucketName}",
        pattern: "${prometheusTargetFile}"
    )
    sh ("rm -f ${prometheusTargetFile}")
}
