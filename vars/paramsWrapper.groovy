Map getDefaultParameters() {
    return [
        GIT_BRANCH: 'develop',
        GCP_PROJECT: gcpUtils.devProject,
        NODE_SELECTOR: jenkinsUtils.determineAgent(),
        SLACK_CHANNEL: '#builds',
        SHARED_LIB_BRANCH: 'master',
        SHARED_LIB_DISPLAY_ALWAYS: false,
        BUILD: true,
        BUILD_NOCACHE: false,
        TEST: true,
        DELIVER: true,
        DEPLOY: true,
        DEPLOY_ALWAYS: false,
        DEPLOY_ATOMIC: true,
        DEPLOY_WAIT: true,
        DEPLOY_WAIT_TIMEOUT: '300',
        DEPLOYMENTS: [],
        FORCE_RESTART: false,
        IMAGE_PULL_POLICY: 'IfNotPresent',
        IMAGE_TAG_PARAMETERS: true,
        IMAGE_TAG_LATEST_BRANCH: 'master',
        IMAGE_TAG_STABLE: true,
        HELM_VALUES: 'Reuse',
        CLEANUP: true,
        <PERSON>LEANUP_ALL: false,
        <PERSON><PERSON>ANUP_WORKSPACE: 'Disabled',
        DEPENDENCIES: false,
        PARAMETERS: false,
        SLACK_DISABLE: false,
        SLACK_DISABLE_SUCCESS: false,
        SLACK_DISABLE_FAILURE: false,
        GITLAB_PROJECT_ID: 'GITLAB_PROJECT_ID',
        RSP_FOLDER_PATH: 'RSP/FOLDER/PATH',
        BUILD_STATE_LAST_CHOICE: '',
        BUILD_STATE_CONFIG_FILTER: '',
        DOCKER_CACHE_REGISTRY: true,
        DOCKER_FORCE_PUSH: false
    ]
}

List toggleableChoice(Map args) {
    List choices = args?.choices ?: []
    String current = args?.current ?: ''
    List result = [current]
    choices.each {
        if (it == current) {
            return
        }
        result.add(it)
    }
    return result
}

Boolean call(Map args) {
    this.defaults = [:]

    this.defaultParameters.each { key, value ->
        this."$key" = value
        this.defaults."$key" = value
    }

    args?.each { key, value ->
        this."$key" = value
        this.defaults."$key" = value
    }

    params?.each { key, value ->
        this."$key" = 'SHARED_LIB_BRANCH' == key ? scmUtils.stripGitRefspecPrefixes(value) : value
    }

    return true
}

Boolean parseDeployment() {
    if ( !binding.hasVariable('DEPLOYMENT') || !this.DEPLOYMENT?.trim() ) {
        echo('Deployment target is not defined, interrupting')
        error(errorWrapper.interruptBuild)
    }
    List deployment = this.DEPLOYMENT.trim().split('/')
    if ( !deployment || deployment.size() != 4 ) {
        error('Incorrect format of DEPLOYMENT parameter value')
    }
    this.GCP_PROJECT = deployment[0]
    this.K8S_CLUSTER = deployment[1]
    this.K8S_NAMESPACE = deployment[2]
    this.HELM_RELEASE = deployment[3]

    return true
}

Boolean updateOnly() {
    if (this.PARAMETERS) {
        error(errorWrapper.updateOnly)
    }
}

List addStandardParameters(Map args) {
    String build = args?.build?.toLowerCase() ?: null
    String deliver = args?.deliver?.toLowerCase() ?: null
    String deploy = args?.deploy?.toLowerCase() ?: null
    String buildStateConfig = args?.buildStateConfig?.toLowerCase() ?: null
    List parameters = args?.parameters ?: []
    List disable = args?.disable ?: []

    if (this.defaults.SHARED_LIB_DISPLAY_ALWAYS || this.defaults.SHARED_LIB_BRANCH != scmUtils.stripGitRefspecPrefixes(this.SHARED_LIB_BRANCH)) {
        parameters.add(listGitBranches(
            name: 'SHARED_LIB_BRANCH',
            defaultValue: this.SHARED_LIB_BRANCH,
            description: "Jenkins shared libraries (https://${gitlabUtils.gitHostname}/devops/jenkins-shared-libraries) GIT branch/tag.\nPersistent parameter",
            type: 'PT_BRANCH_TAG',
            selectedValue: 'DEFAULT',
            quickFilterEnabled: true,
            sortMode: 'ASCENDING_SMART',
            remoteURL: gitlabUtils.gitSshUrl + ':devops/jenkins-shared-libraries.git',
            credentialsId: scmUtils.credentialsId
        ))
    }

    if (build) {
        parameters.addAll([
            booleanParam(
                name: 'BUILD',
                defaultValue: this.defaults.BUILD,
                description: 'Whether to run CI build or not'
            ),
            booleanParam(
                name: 'BUILD_NOCACHE',
                defaultValue: this.defaults.BUILD_NOCACHE,
                description: 'Whether to run CI build with caching disabled or not. Useful for development purposes'
            ),
            booleanParam(
                name: 'TEST',
                defaultValue: this.defaults.TEST,
                description: 'Whether to run CI test or not'
            )
        ])
    }

    if (deliver || deploy) {
        parameters.add(booleanParam(
            name: 'DELIVER',
            defaultValue: this.defaults.DELIVER,
            description: 'Whether to run CD (delivery) or not'
        ))
    }

    if (deploy) {
        parameters.add(booleanParam(
            name: 'DEPLOY',
            defaultValue: this.defaults.DEPLOY,
            description: 'Whether to run CD (deployment) or not'
        ))
    }

    if (['helm', 'kubectl'].contains(deploy)) {
        parameters.addAll([
            booleanParam(
                name: 'DEPLOY_ATOMIC',
                defaultValue: this.DEPLOY_ATOMIC,
                description: 'Whether to rollback during CD (deployment) in case of failure.\nPersistent parameter'
            ),
            booleanParam(
                name: 'DEPLOY_WAIT',
                defaultValue: this.DEPLOY_WAIT,
                description: 'Whether to wait for CD (deployment) completion or not.\nPersistent parameter'
            ),
            stringParam(
                name: 'DEPLOY_WAIT_TIMEOUT',
                defaultValue: this.DEPLOY_WAIT_TIMEOUT,
                description: 'Wait timeout for CD (deployment) completion in seconds.\nPersistent parameter'
            ),
            choice(
                name: 'DEPLOYMENT',
                choices: this.defaults.DEPLOYMENTS,
                description: 'Deployment target'
            )
        ])

        if ('helm' == deploy) {
            parameters.addAll([
                booleanParam(
                    name: 'FORCE_RESTART',
                    defaultValue: this.DEPLOY_ALWAYS ? true : this.FORCE_RESTART,
                    description: 'Whether to force restart of deployment or not.\n' + (this.DEPLOY_ALWAYS ? '' : 'Persistent parameter.\n') + 'Useful for development purposes'
                ),
                choice(
                    name: 'IMAGE_PULL_POLICY',
                    choices: this.toggleableChoice(
                        current: this.DEPLOY_ALWAYS ? 'Always' : this.IMAGE_PULL_POLICY,
                        choices: ['IfNotPresent', 'Always']
                    ),
                    description: 'Container image pull policy.\nSee https://kubernetes.io/docs/concepts/containers/images/#updating-images for more details.\n' + (this.DEPLOY_ALWAYS ? '' : 'Persistent parameter.\n') + 'Useful for development purposes'
                ),
                choice(
                    name: 'HELM_VALUES',
                    choices: this.toggleableChoice(
                        current: this.HELM_VALUES,
                        choices: ['Reuse', 'Merge']
                    ),
                    description: 'Controls values handling for deployment (Helm release).\n"Reuse" means "--reuse-values" flag, "Merge" adds "--reset-values" flag and "-f" pointing to the file containing all values defined in current deployment (if any) so Helm will take care of values merge.\nPersistent parameter.\nUseful for development purposes (e.g. adding new values like environmental variables)'
                )
            ])
        }
    }

    if ('docker' == build && this.IMAGE_TAG_PARAMETERS) {
        parameters.addAll([
            string(
                name: 'IMAGE_TAG_LATEST_BRANCH',
                defaultValue: this.IMAGE_TAG_LATEST_BRANCH,
                description: 'Tag container image additionally as "latest" if refspec matches the value of this parameter, add refspec slug as a suffix to container image tag otherwise.\nSet to empty to omit tagging any image as "latest".\nPersistent parameter.\nUseful for development purposes'
            ),
            booleanParam(
                name: 'IMAGE_TAG_STABLE',
                defaultValue: this.IMAGE_TAG_STABLE,
                description: 'Whether to use only application version (defined in Helm Chart) as a container image tag or also include short SHA and build number.\nTriggers redeployment and restart if disabled.\nPersistent parameter.\nUseful for development purposes'
            )
        ])
    }

    if (build || deliver || deploy) {
        parameters.add(booleanParam(
            name: 'CLEANUP',
            defaultValue: this.defaults.CLEANUP,
            description: 'Whether to run cleanup or not' + ('docker' == build ? '.\nPrunes dangling Docker containers and images' : '')
        ))
    }

    if ('docker' == build) {
        parameters.add(booleanParam(
            name: 'CLEANUP_ALL',
            defaultValue: this.defaults.CLEANUP_ALL,
            description: 'Whether to prune all Docker images during cleanup or not'
        ))
    }

    parameters.add(choice(
        name: 'CLEANUP_WORKSPACE',
        choices: this.toggleableChoice(
            current: this.defaults.CLEANUP_WORKSPACE,
            choices: ['Disabled', 'CleanBeforeCheckout', 'CleanAfterCheckout', 'WipeBeforeBuild', 'WipeAfterBuild']
        ),
        description: 'Whether to clean untracked repository content in workspace before/after checkout or wipe out the whole workspace before/after build'
    ))

    if (deliver || deploy) {
        parameters.add(booleanParam(
            name: 'DEPENDENCIES',
            defaultValue: this.defaults.DEPENDENCIES,
            description: 'Whether to install external dependencies or not'
        ))
    }

    if (buildStateConfig) {
        parameters.add(hidden(
            name: 'RSP_FOLDER_PATH',
            defaultValue: this.defaults.RSP_FOLDER_PATH,
            description: 'Path where RSP files are located.\nPersistent parameter'
        ))
        parameters.add(hidden(
            name: 'GITLAB_PROJECT_ID',
            defaultValue: this.defaults.GITLAB_PROJECT_ID,
            description: 'Project ID from which to take the list of BUILD.\nPersistent parameter'
        ))
        parameters.add(hidden(
            name: 'BUILD_STATE_LAST_CHOICE',
            defaultValue: this.defaults.BUILD_STATE_LAST_CHOICE,
            description: 'Last build BUILD_STATE parameter choice'
        ))
        parameters.add(hidden(
            name: 'BUILD_STATE_CONFIG_FILTER',
            defaultValue: this.defaults.BUILD_STATE_CONFIG_FILTER,
            description: 'Filter for BUILD_STATE parameter'
        ))
        parameters.add($class: 'CascadeChoiceParameter',
            choiceType: 'PT_SINGLE_SELECT',
            filterLength: 1,
            filterable: false,
            name: 'BUILD_STATE',
            referencedParameters: 'GITLAB_PROJECT_ID,RSP_FOLDER_PATH,GIT_BRANCH,BUILD_STATE_LAST_CHOICE,BUILD_STATE_CONFIG_FILTER',
            script: [
                $class: 'GroovyScript',
                fallbackScript: [classpath: [], sandbox: true, script: 'return "[Could not get files. Please check RSP_FOLDER_PATH and GITLAB_PROJECT_ID parameters]"'],
                script: [
                    classpath: [],
                    sandbox: false,
                    script: """import groovy.json.JsonSlurper
import java.nio.file.Files
import java.nio.file.Paths

def gitlabUrl = "https://${gitlabUtils.gitHostname}/api/v4/projects/\${GITLAB_PROJECT_ID}/repository/tree"

def jenkinsHome = System.getenv("JENKINS_HOME")
def tokenFilePath = "\${jenkinsHome}/token"
def privateToken = new String(Files.readAllBytes(Paths.get(tokenFilePath))).trim()

def folderPath = "\${RSP_FOLDER_PATH}"
def branchName = "\${GIT_BRANCH.replaceFirst('^origin/', '')}"

def apiUrl = "\${gitlabUrl}?path=\${folderPath}&ref=\${branchName}&per_page=100"

def connection = new URL(apiUrl).openConnection()
connection.setRequestProperty("PRIVATE-TOKEN", privateToken)

def response = connection.inputStream.withCloseable { it.text }
def json = new JsonSlurper().parseText(response)

def files = json.findAll { it.type == "blob" }.collect {
    def nameWithoutExtension = it.name.split("\\\\.")[0]
    return nameWithoutExtension
}

if (files.empty) {
    return "[Could not get files. Please check RSP_FOLDER_PATH and GITLAB_PROJECT_ID parameters]"
}

def configFilter = "\${BUILD_STATE_CONFIG_FILTER}".trim()
if (!configFilter.isEmpty()) {
    files = files.findAll { it.contains(configFilter) }
}

def lastChoice = "\${BUILD_STATE_LAST_CHOICE}".trim()
if (files.contains(lastChoice)) {
    files.remove(lastChoice)
    files.add(0, lastChoice)
}

if (files.empty) {
    return "[No files match the filter: \${configFilter}]"
}

return files
"""
                ]
            ]
        )
    }

    parameters.add(booleanParam(
        name: 'PARAMETERS',
        defaultValue: this.defaults.PARAMETERS,
        description: 'Only update job parameters instead of running all stages'
    ))

    return parameters.findAll {
        return !(disable.size() && disable.contains(it?.arguments?.name))
    }
}
