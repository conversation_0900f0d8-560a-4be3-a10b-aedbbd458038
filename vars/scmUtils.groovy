import groovy.transform.Field

@Field String gitTool = 'Default'

String getHttpsByGitUrl(String url) {
    return url ? 'https://' + url.split('@')[1]?.replace(':', '/')?.replaceAll('\\.git$', '') : null
}

def getGitBrowser(def scm) {
    if (scm?.userRemoteConfigs[0]?.url?.startsWith(gitlabUtils?.gitSshUrl) ||
        scm?.userRemoteConfigs[0]?.url?.startsWith(gitlabUtils?.gitSshUrlInternal) ||
        scm?.userRemoteConfigs[0]?.url?.startsWith(gitlabUtils?.gitSshUrlDeprecated)
    ) {
        return [$class: 'GitLab',
            repoUrl: this.getHttpsByGitUrl(scm?.userRemoteConfigs[0]?.url),
            version: gitlabUtils.version
        ]
    }
    return scm?.browser ?: null
}

def getGitBranches(def scm) {
    if (gitlabUtils?.branch && gitlabUtils?.repoSshUrl &&
        (scm?.userRemoteConfigs[0]?.url == gitlabUtils?.repoSshUrl ||
        scm?.userRemoteConfigs[0]?.url == gitlabUtils?.repoSshUrlInternal ||
        scm?.userRemoteConfigs[0]?.url == gitlabUtils?.repoSshUrlDeprecated)
    ) {
        return [this.fixupGitRefspecPrefixes(gitlabUtils.branch)]
    }
    return scm?.branches ? this.fixupGitRefspecPrefixes(scm.branches) : []
}

List fixupGitRefspecPrefixes(List arg) {
    return arg?.collect {
        if (it instanceof hudson.plugins.git.BranchSpec) {
            String branchSpec = it // single time casting to String
            // regexp for matching Groovy normal identifiers
            // see https://groovy-lang.org/syntax.html#_normal_identifiers
            // codenarc-disable GStringExpressionWithinString
            if (branchSpec ==~ /^\$[\u007B]?[a-zA-Z0-9_\-\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF\u0100-\uFFFE]+[\u007D]?$/) {
                // codenarc-enable
                def engine = new groovy.text.GStringTemplateEngine()
                return this.fixupGitRefspecPrefixes(engine.createTemplate(branchSpec).make(env.getEnvironment()).toString())
            }
            return this.fixupGitRefspecPrefixes(branchSpec)
        } else if (it instanceof Map && it?.name) {
            return this.fixupGitRefspecPrefixes(it?.name)
        }
        return it
    }
}

Map fixupGitRefspecPrefixes(String arg) {
    return [name: this.stripGitRefspecPrefixes(arg)]
}

String stripGitRefspecPrefixes(String arg) {
    return arg?.replaceFirst('^refs/heads/', '')?.replaceFirst('^origin/', '')
}

List addExtensions(List extensions, def params) {
    switch (params?.CLEANUP_WORKSPACE) {
        case 'CleanBeforeCheckout':
            extensions.add([$class: 'CleanBeforeCheckout', deleteUntrackedNestedRepositories: true])
            break
        case 'CleanAfterCheckout':
            extensions.add([$class: 'CleanCheckout', deleteUntrackedNestedRepositories: true])
            break
        case 'WipeBeforeBuild':
            extensions.add([$class: 'WipeWorkspace'])
            break
        case 'WipeAfterBuild':
            cleanWs()
            break
    }
    return extensions
}

def checkoutGit(def scm) {
    return checkout([$class: 'GitSCM',
        branches: this.getGitBranches(scm),
        browser: this.getGitBrowser(scm),
        doGenerateSubmoduleConfigurations: scm.doGenerateSubmoduleConfigurations ?: false,
        extensions: scm.extensions ?: [],
        gitTool: scm.gitTool ? scm.gitTool : this.gitTool,
        submoduleCfg: scm.submoduleCfg ?: [],
        userRemoteConfigs: scm.userRemoteConfigs ?: []
    ])
}

def checkoutGit(def scm, def params) {
    return checkout([$class: 'GitSCM',
        branches: this.getGitBranches(scm),
        browser: this.getGitBrowser(scm),
        doGenerateSubmoduleConfigurations: scm.doGenerateSubmoduleConfigurations ?: false,
        extensions: this.addExtensions(scm.extensions ?: [], params),
        gitTool: scm.gitTool ? scm.gitTool : this.gitTool,
        submoduleCfg: scm.submoduleCfg ?: [],
        userRemoteConfigs: scm.userRemoteConfigs ?: []
    ])
}

String getCredentialsId() {
    return gitlabUtils.credentialsId
}

Boolean call(Map args) {
    if ( args?.scm?.toLowerCase() == 'git' ) {
        this.GIT_BRANCH_REFSPEC = stripGitRefspecPrefixes(args?.checkout?.GIT_BRANCH)
        this.GIT_BRANCH_REFSPEC_SLUG = this.GIT_BRANCH_REFSPEC.toLowerCase().replaceAll('/', '-')
        this.GIT_COMMIT = args?.checkout?.GIT_COMMIT
        this.GIT_COMMIT_SHORT_SHA = this.GIT_COMMIT.take(8)
        this.GIT_URL = this.getHttpsByGitUrl(args?.checkout?.GIT_URL)

        try {
            if (isUnix()) {
                this.GIT_TAG_REFSPEC = sh(label: 'Retrieving GIT tag refspec', script: 'git describe --tags', returnStdout: true).trim()
            } else {
                this.GIT_TAG_REFSPEC = bat(label: 'Retrieving GIT tag refspec', script: '@git describe --tags', returnStdout: true).trim()
            }
        } catch (error) {
            this.GIT_TAG_REFSPEC = ''
        }

        try {
            if (isUnix()) {
                this.GIT_CACHE_BRANCH_REFSPEC = sh(label: 'Retrieving GIT branch refspec for registry cache', script: 'git branch --remote --contains | head -1', returnStdout: true).trim()
                this.GIT_CACHE_BRANCH_REFSPEC_SLUG = stripGitRefspecPrefixes(this.GIT_CACHE_BRANCH_REFSPEC).toLowerCase().replaceAll('/', '-')
            } else {
                this.GIT_CACHE_BRANCH_REFSPEC = bat(label: 'Retrieving GIT branch refspec for registry cache', script: '@git branch --remote --contains', returnStdout: true).trim()
                this.GIT_CACHE_BRANCH_REFSPEC_SLUG = stripGitRefspecPrefixes(this.GIT_CACHE_BRANCH_REFSPEC).toLowerCase().replaceAll('/', '-')
            }
        } catch (error) {
            this.GIT_CACHE_BRANCH_REFSPEC = ''
            this.GIT_CACHE_BRANCH_REFSPEC_SLUG = ''
        }

        return true
    }
    return false
}

Map variables(Map args) {
    if (this.call(args)) {
        return this
    }
    return [:]
}
