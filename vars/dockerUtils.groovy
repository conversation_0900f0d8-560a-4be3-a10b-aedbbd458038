Boolean call(Map args) {
    def registryName = args?.registryName ?: 'eu.gcr.io'
    def credentialsId = args?.registryCredentials ?: "gcr:${gcpUtils.infraProject}"
    def srcRegistryName = args?.srcRegistryName ?: registryName
    def srcCredentialsId = args?.srcRegistryCredentials ?: credentialsId
    def dstRegistryName = args?.dstRegistryName ?: registryName
    def dstCredentialsId = args?.dstRegistryCredentials ?: credentialsId

    def buildArgs = args?.buildArgs ?: './'
    def imageUri = args?.imageUri
    def imageTags = args?.imageTags ?: args?.imageTag ? [args.imageTag] : ['latest']
    def cleanupArgs = args?.cleanupArgs ?: '--filter until=720h'

    def cacheMainBranches = args?.cacheMainBranches ?: ['master', 'release', 'dev', 'develop']
    def cacheRegistry = args?.cacheRegistry ?: 'eu.gcr.io/testsre'
    def cacheBuildArgs = args?.cacheBuildArgs ?: ''

    return [
        registryName: registryName,
        credentialsId: credentialsId,
        srcRegistryName: srcRegistryName,
        srcCredentialsId: srcCredentialsId,
        dstRegistryName: dstRegistryName,
        dstCredentialsId: dstCredentialsId,
        buildArgs: buildArgs,
        imageUri: imageUri,
        imageTags: imageTags,
        cleanupArgs: cleanupArgs,
        cacheRegistry: cacheRegistry,
        cacheMainBranches: cacheMainBranches,
        cacheBuildArgs: cacheBuildArgs
    ]
}

Boolean buildImage(Map args) {
    def config = call(args)

    if ( !args?.params?.BUILD ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    if ( args?.params?.IMAGE_TAG_LATEST_BRANCH == args?.scmVars?.GIT_BRANCH_REFSPEC ) {
        config.imageTags.add('latest')
    }

    if ( args?.params?.DOCKER_CACHE_REGISTRY ) {
        dockerImageStore = sh(label: 'Check if containerd image store is enabled', script: "docker info -f '{{ .DriverStatus }}'", returnStdout: true).trim()
        if ( dockerImageStore.contains('driver-type io.containerd.snapshotter.v1') ) {
            if ( config.cacheBuildArgs?.isEmpty() ) {
                imageName = config.imageUri.substring(config.imageUri.lastIndexOf("/") + 1)
                if ( imageName.contains(':') ) {
                    imageName = imageName.substring(0, imageName.lastIndexOf(":"))
                }
                imageBranch = args?.scmVars?.GIT_CACHE_BRANCH_REFSPEC_SLUG
                config.cacheBuildArgs += "--cache-to type=registry,ref=$config.cacheRegistry/$imageName-cache:$imageBranch,mode=max "

                if ( !config.cacheMainBranches.contains(args?.scmVars?.GIT_CACHE_BRANCH_REFSPEC_SLUG) ) {
                    config.cacheMainBranches.add(args?.scmVars?.GIT_CACHE_BRANCH_REFSPEC_SLUG)
                }

                config.cacheMainBranches.each {
                    config.cacheBuildArgs += "--cache-from type=registry,ref=$config.cacheRegistry/$imageName-cache:$it,mode=max "
                }
            }
        } else {
            print('Containerd image store is disabled. Build cannot use remote registry cache')
        }
    }

    String dockerignore = ''
    if ( fileExists('.dockerignore') ) {
        dockerignore = readFile(file: '.dockerignore')
    }
    dockerignore += '''
**/.kubeconfig.*
**/.gcloud.*
gke_gcloud_auth_plugin_cache
*helm-release-values*
*helm_package*
'''
    writeFile(file: '.dockerignore', text: dockerignore)

    withEnv(['DOCKER_BUILDKIT=1']) {
        def dockerImage
        withDockerRegistry([credentialsId: config.srcCredentialsId, url: "https://${config.srcRegistryName}"]) {
            dockerImage = docker.build(config.imageUri, (args?.params?.BUILD_NOCACHE ? '--no-cache --pull ' : config.cacheBuildArgs) + config.buildArgs)
        }

        if ( args?.params?.DELIVER || args?.params?.DOCKER_FORCE_PUSH ) {
            withDockerRegistry([credentialsId: config.dstCredentialsId, url: "https://${config.dstRegistryName}"]) {
                config.imageTags.each {
                    dockerImage.push(it)
                }
            }
        }
    }

    return true
}

Boolean cleanup(Map args) {
    def config = call(args)

    if ( !args?.params?.CLEANUP ) {
        jenkinsUtils.markStageSkipped()
        return true
    }

    String scriptClean = "docker container prune -f; docker image prune -f ${config.cleanupArgs}"
    String scriptCleanAll = 'docker image prune -af'

    if ( isUnix() ) {
        sh(label: 'Cleaning up', script: scriptClean)
    } else {
        bat(label: 'Cleaning up', script: scriptClean)
    }

    if ( args?.params?.CLEANUP_ALL ) {
        if ( isUnix() ) {
            return sh(label: 'Cleaning up everything', script: scriptCleanAll)
        }

        return bat(label: 'Cleaning up everything', script: scriptCleanAll)
    }
}
