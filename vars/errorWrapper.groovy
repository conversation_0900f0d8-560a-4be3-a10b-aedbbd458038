import groovy.transform.Field

@Field String updateOnly = 'updateOnly'
@Field String interruptBuild = 'interruptBuild'

Boolean call(Exception error) {
    if (error?.message == this.updateOnly) {
        currentBuild.result = 'ABORTED'
        echo('Aborting job due to parameters update request')
        gitlabUtils.update(state: 'canceled')
        envUtils.deinit()
        return true
    } else if (error?.message == this.interruptBuild) {
        currentBuild.result = 'SUCCESS'
        echo('The build was interrupted')
        gitlabUtils.update(state: 'canceled')
        envUtils.deinit()
        return true
    }
}
