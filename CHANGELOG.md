# CHANGELOG

<!--- next entry here -->

## 0.24.0
2025-07-31

### Features

- **npmservice:** added unpublishNpm method (084d51e22e2456313bc030ddc4b875af4e4c5ef2)

## 0.23.0
2025-07-30

### Features

- **dockerconstants:** bump documentation-render docker image version (8a3fa0e080642d26cc559f4aa7ef762c452a3157)

## 0.22.0
2025-07-17

### Features

- **src,vars:** added check file existance [SRE-1195] (e07185bdeb2d8d2ff10b3cf7a55b8e3785f41828)

## 0.21.0
2025-07-08

### Features

- **src:** added classes for validate and build npm packages [SRE-1195] (cc26d02073f05d8d2a761f64d1f6cb7c4ddd4717)

## 0.20.0
2025-05-30

### Features

- **monitoringutils:** add logic for push prometheus targets [SRE-1154] (fdce71deb584dd182ea605bccb46cc86919a5ddc)

## 0.19.0
2025-05-30

### Features

- **monitoringutils:** add logic for push prometheus targets [SRE-1154] (e6c16185af2c1e7caf3335fa96bade5f7dd78a38)

## 0.18.0
2025-05-07

### Features

- **paramswrapper, dockerutils:** add DOCKER_FORCE_PUSH [SRE-1131] (767ee55cf1b2f16f2b694aacf8be671ecf8c2dfc)

## 0.17.0
2025-04-30

### Features

- **paramswrapper, jenkinsutils:** choose jenkins agent based on day and time [SRE-466] (538c88711bc53a3fcdd4ca5489f0011ef5ac13b6)

## 0.16.2
2025-03-26

### Fixes

- **dockerutils:** deal with imageUri with tag included in docker build for remote caches [SRE-466] (64b124efd8f425c88410d90892d58ede2a98a62a)

## 0.16.1
2025-03-25

### Fixes

- **helmutils, scmutils:** explicitly set bash shell, registry cache branch fix [SRE-466] (6e3a57ea4e8f67c4d623911d364cd754b17fbe3b)

## 0.16.0
2025-03-25

### Features

- **dockerutils, scmutils, paramswrapper:** docker cache in remote registry [SRE-466] (8ccb53f3ae4821fdde847443df27bbf49810ebe9)

## 0.15.0
2025-03-04

### Features

- **gitlabutils:** fix branch names into gitlab api url [SRE-1041] (a4fda55d0f4d1515e6cbab7367a17cbcb4392f7c)

## 0.14.0
2025-02-27

### Features

- **helmutils:** use exact version of helm chart during helm upgrade (ee5f2cc072f595a9bcf75449bdf26516b68b2a53)

## 0.13.0
2025-02-26

### Features

- **monitoringutils, gitlabutils:** add monitoringUtils and download gitlab files/archives through API (6344d28810d657c36ce71a67465fe8e365defbaf)

## 0.12.1
2024-09-24

### Fixes

- **buildstateconfigurator:** remove manifest if MANIFEST_SOURCE exists [SRE-844] (67ed68cd0d575d7ceb3d5441111b6751533d81ea)

## 0.12.0
2024-09-19

### Features

- **buildstateconfigurator:** added buildStateConfigurator method for client builds [SRE-847] (a51f1ed0a788215fdecf044adf59770fafc0f80e)
- **paramswrapper:** added buildStateConfig parameter into paramsWrapper [SRE-844] (c079784a36e3d4ba1940ebf05cb5b0368d1d2f32)

## 0.11.2
2024-07-24

### Fixes

- **slackutils:** mattermost instead of slack [SRE-791] (e5145a4cd5b55bbaa9e2e8e5dba24873fa00a352)

## 0.11.1
2023-01-31

### Fixes

- **slackutils:** linter fix, and changelog (3c04aafcd43fa46cd30dc88be96ccd54fcd509da)

## 0.11.0
2022-07-16

### Features

- **helmutils:** added support for setting --values arg for Helm [SRE-293] (64eba01cb0376bedc5dcbfd662ba654a844e380a)
- **helmutils:** optimized getHelmDirectoryPath function [SRE-293] (7578e3102946ceac14941a9963986ae08b69d300)
- **helmutils:** added ensureHelmValue argument for deployChart() [SRE-293] (c6d70f59eb33181b77bbed47db45b02409c0d035)
- **helmutils:** added update arg which affects releaseInfo() cache [SRE-293] (7308d5858be1f9123a634136b548b933650328a8)
- **helmutils:** added condition for defining registryName & repositoryName [SRE-293] (e7c38f3f48b00483c1d74560d6e06e8bac9299b8)

## 0.10.0
2022-07-08

### Features

- **helmutils:** use GIT_TAG_REFSPEC for appVersion as a last restort [SRE-451] (c655248cebb544c702ac222a3fe02499296fd922)

## 0.9.0
2022-04-22

### Features

- **helmutils:** added ensureAdditional argument for deployChart() [SRE-293] (b67d018eb7acbef0c54b2227b25eae4968835b39)

## 0.8.0
2022-03-15

### Features

- **dockerutils:** enabled buildkit [SRE-333] (e3568361cef82ae8f1373e8cca6430b5302d3cf7)

### Fixes

- **dockerutils:** implemented multi-registry authentication [SRE-320] (cb03b00b36c5e0d41691baf17be5512eeec2d74f)

## 0.7.2
2022-02-19

### Fixes

- **dockerutils:** added "--pull" option when BUILD_NOCACHE enabled [SRE-333] (e41d05e6fcc197ab0566ce28b3ded69335aafe15)

## 0.7.1
2022-02-16

### Fixes

- **paramswrapper:** altered IMAGE_TAG_LATEST_BRANCH parameter description (20a1b6e7bd5256323ef8f9a131c70481be54a7d1)

## 0.7.0
2022-02-14

### Features

- **helmutils:** altered image tag stable scheme [SRE-320] (7078459cd450a2fb950b8a5fe395fb6a4ab1d0f4)
- **helmutils:** allow assigning imageTag directly [SRE-320] (ca83edb875fd7cbb00b29babe0a728903af5de95)

## 0.6.3
2022-02-11

### Fixes

- **paramswrapper:** altered description of multiple params [SRE-320] (edad98a544e978ed0836ff58502921c3f451c38c)

## 0.6.2
2022-02-11

### Fixes

- **helmutils:** added check for presence of HELM_RELEASE parameter [SRE-320] (675f5b688251bca4738b263714e202e9ff0a5e2d)
- **scmutils:** strip GIT refspec prefixes properly [SRE-320] (e0d661820c544a2dc90248a6d246ca65b08165ac)
- **dockerutils:** use GIT refspec for comparing instead of refspec slug [SRE-320] (ea5c3d0c910e927830a66f66afbca5063dea1f5f)

## 0.6.1
2022-02-07

### Fixes

- **helmutils:** enabled debug output for Helm rollback process (f53cad6144597327a65faec63170e5eb034be31e)

## 0.6.0
2022-02-04

### Features

- **loader/libutils:** disabled changelog insertion (db4a86a6e80563f85e08b9fcef18a13e0a40ee84)
- **scmutils:** enabled conditional bypass for gitTool (def23dedf97f5a204b7e388e235b7f076a6b5055)
- **scmutils:** added fixupGitRefspecPrefixes (f04fea7af4e2b7bbf2c730d239f2784c3bd2d0f2)

### Fixes

- **loader/libutils:** altered replacement order in stripGitRefspecPrefixes (6b5579fc9963343af296651b37000ba0a26d957d)
- **scmutils:** altered replacement order in stripGitRefspecPrefixes (0ba6e42b0cbfb1f462ee3f821b91e63d62f8ecfa)

## 0.5.0
2022-01-24

### Features

- **paramswrapper:** added SHARED_LIB_DISPLAY_ALWAYS parameter [SRE-291] (9b84242660b60d9372f7b23e3dc3f41a33fbddec)

### Fixes

- **paramswrapper:** got rid of ParameterReassignment linter warning (813d64726cbc71db1dc67d169a22a4dadc6b8cbc)

## 0.4.3
2021-12-09

### Fixes

- **slackutils:** altered timings indication in case of multiple reasons (d4e35b4bcf20746314e64e3ec70e2e9fa8c5fcde)

## 0.4.2
2021-07-20

### Fixes

- **helmutils:** added exception handling for revoking of GCE-provided credentials (c2c5d7ba4a1db34f58a6c09f28d086d921989bc5)

## 0.4.1
2021-07-12

### Fixes

- **helmutils:** implemented overriding of K8s object name [SRE-179] (14c0a332324d72b53b9aa5d5bc6b1be4acd2ad4b)

## 0.4.0
2021-07-09

### Features

- **loader/libutils:** added dynamic libraries loader (2f5fdc0ec9051e6c93cf15c17b220f1446b2624a)

### Fixes

- **paramswrapper:** added parentheses for NODE_SELECTOR specificator (c059c9f6971ac3315af7900774f1a921ef77a6f3)
- **slackutils:** removed unnecessary new line from message header (fea6525977efba1ef66576aaf69c67aa4025221d)

## 0.3.0
2021-07-07

### Features

- **helmutils:** enabled debug output for Helm upgrade process (657ba0aaf793344f37559eaf83b5787f2c01455b)
- **helmutils:** added retrieval of Helm release status before upgrade (b46814c8e4db276a030a6645f8a651d1e382b6de)

## 0.2.4
2021-07-07

### Fixes

- **paramswrapper:** added DEPLOY_ATOMIC parameter (f56faaf1852b4d6391dda2250dee8fcc4d4db7fc)
- **helmutils:** added explicit rollback in case of upgrade failure (a4d73ad45be84d5e98b5ce1c311786f1bb65668d)

## 0.2.3
2021-07-02

### Fixes

- **helmutils:** no more failures when DEPLOY_WAIT is disabled [SRE-236] (accc7bdda0e444c25fbb151750d61106a7931601)

## 0.2.2
2021-07-01

### Fixes

- **helmutils:** retrieve logs by instance label instead of API resource (d7bf1ef90d99e441669cfcb9b56ecfe8469b49b4)

## 0.2.1
2021-06-28

### Fixes

- **jenkinsutils:** added explicit return for markStageSkipped() (a15b4d2d55e90c4f3523d559637d613abc0cf569)
- **paramswrapper:** reduced return condition for addStandardParameters() (7575c3186e47f4b890b7a7bd959ed57bf0eab7d5)
- **paramswrapper:** corrected HELM_VALUES parameter description (c7b52a1deaad90919b2d8b15c0a10b6e39fa645a)
- **helmutils:** return values properly from releaseInfo() [SRE-226] (d41dfafa6c81209db6859bf6fdb9c861dc882670)
- **paramswrapper:** set all parameters configurable [SRE-226] (0787906bf8be3a416c32367831d65e973ca28d3d)
- **paramswrapper:** improved HELM_VALUES parameter [SRE-226] (d47dbcfcd46a4f9716274d8db82cff69f38fac9d)
- **helmutils:** improved HELM_VALUES parameter handling [SRE-226] (e333f37105543294acee919e9f9a3aba636d4b12)

## 0.2.0
2021-06-23

### Features

- **paramswrapper:** added parameters filtering (8cc75088298393f081648c6d2fdd2d1b0d01d25f)

## 0.1.2
2021-06-23

### Fixes

- **slackutils:** revert "improved readability of paramsFilter condition" (c312db3754e22469f087c07089f1e46e389c24c0)

## 0.1.1
2021-06-23

### Fixes

- **helmutils:** eliminated unnecessary Helm repo update during deploy (5262ec102d7ebbb4e45d919f1db97c5daa1cb724)

## 0.1.0
2021-06-23

### Features

- **paramswrapper:** added conditions for BUILD, TEST, DELIVER and CLEANUP (44a3db37441a3087634f6f6dfa4808a1a525343c)
- **paramswrapper:** replaced HELM_MERGE_VALUES parameter with HELM_VALUES (6ecb84aef3a4af51d733a67b3b26139f984720bb)
- **helmutils:** added support for handling of HELM_VALUES parameter (7b0684d86754b140e2135320a3d69ede9931f401)
- **helmutils:** implemented container image vars overriding [SRE-219] (dfece1f944c72a1495f86d0ba2ec999d9be26d26)
- **helmutils:** implemented repo/chart update during deploy [SRE-219] (22f2745057f597d69706560e48db1cf5e4881cc6)
- **slackutils:** added build causes indication (8c28f88cb0be39f93552c83cce0331a3cfaf7e19)
- **slackutils:** improved changes indication (e8df9137f24cec32464765a58626ff34c565681f)
- **slackutils:** improved build duration indication (cd329d48448337fdaf45c20d9c5c9c0c51409603)
- **when:** added when.groovy from github.com/comquent/imperative-when (a757478d6b4329718d1cb814e9e3c1e6ff7409e0)
- **jenkinsutils:** added jenkinsUtils.groovy based on when.groovy (b9ac86996b1dfbb710042dac22a42d9c1171a476)
- **dockerutils:** added markStageSkipped() call for skipped stages (f878981394eeb54359b565bc2fcb34896b933b1a)
- **helmutils:** added markStageSkipped() call for skipped stages (c42809786535ab10e96f56470ca6372b336f6df7)
- **slackutils:** added markStageSkipped() call for skipped stages (cb419b5cb323e9768b3894e88aaa7cba4c387828)
- **scmutils:** added getGitBrowser method (cca0eee544b02fd39bbcbbba13b9b4f24d1ef52d)
- **gitlabutils:** added version Groovy field (8aae0a0781686c3082cabf7796e96f3f1199366d)
- **scmutils:** added gitTool for checkout() method call (ff616382ba216922e4eb1ece8ca76ccf44f38bdf)

### Fixes

- **scmutils:** improved code formatting of conditions (bd9af68c2317cfd21daaed6b58f99a9508885be0)
- **slackutils:** improved code formatting of conditions (9cc8c92ad0646b14103c65543a7dd3e0abdc07a1)
- **slackutils:** improved readability of paramsFilter condition (c6ca4bf79c1941e2baad8e4d84807da4615fa892)
- **scmutils:** multiple conditions replaced with a single switch (abe140be0bc0b95b1713a7b6586bac9aec16f255)
- **paramswrapper:** improved readability of multiple conditions (c04167eb339d4027db9799f37e50bcb124234f3f)
- **helmutils:** inverted multiple conditions for better readability (1972cd7f462bc7573789aab4c0181824dc127830)
- **helmutis:** replaced deployArgs with set [SRE-219] (54e7af7c2ca999e97ea08b5873321cb663e0245f)
- **helmutils:** defined specific static type for multiple objects (b2408fc1bb40038256a9dade042321a73f23b469)
- **dateutils:** defined specific static type for multiple objects (5bdb0af059eb914583f3a52400783e1c5e73df27)
- **errorwrapper:** defined specific static type for error object (5e89c3c565f96d98efa23fad31b1dc22d67764f3)
- **dockerutils:** added explicit return for multiple methods (020b7dad80658d2a1cedc987d6977c555435650c)
- **slackutils:** added explicit return for success() and failure() (b57a8363bf255a0bf5a6619a562fae97d78f170b)
- **slackutils:** replaced maxMsgLen property getter with Groovy field (bd2e70f06cbc6f3189b264cdfac25ba98370406d)
- **gcputils:** replaced multiple property getters with Groovy fields (d55d32035835e4df9f08d897e104254db9bfdc63)
- **gitlabutils:** replaced multiple property getters with Groovy fields (c1683b5a72df2f507bc25d8397664aea2fd4c0d9)
- **errorwrapper:** replaced multiple property getters with Groovy fields (6d99acc4e8d008c4c3831e795f385425f9cc61e6)
- **paramswrapper:** added explicit return for multiple methods (e9dab891b26b97bc65ae1c55c4818bee25fb396c)
- **helmutils:** eliminated multiple linter notices (85eba5a1ec73ff613087368e1bf8d79bdcfea693)
- **scmutils:** enabled stripGitRefspecPrefixes call for each GIT branch (****************************************)
- **helmutils:** disabled CodeNarc ImplicitReturnStatement for deliverChart() (e68760821fc45eaaf18bfee091a3fa691d68ad6c)

## 0.0.4
2021-06-12

### Fixes

- **paramswrapper:** added HELM_MERGE_VALUES parameter [SRE-125] (9b349582a6166a2eed267c14e120f10f36f8530d)
- **helmutils:** implemented handler for HELM_MERGE_VALUES [SRE-125] (d2c19e978b5294c5f08c640f0a11e753374ba59a)
- **envutils:** changed order of init/deinit and altered delete pattern (189631e846ff862a9958ce74a2b47ea10259d512)
- **paramswrapper:** use stripGitRefspecPrefixes instead of replaceFirst (70f814ca7c295821a8539b7f8aa33ecba419b2ba)
- **gitlabutils:** added gitHostname* properties (04b0f1267bb3deed22cd75f6395f917eaf8d8246)
- **slackutils:** added to header indication of shared library branch (dc669d3d45e2c29de1af16575eb82e7f19c311a6)

## 0.0.3
2021-06-11

### Fixes

- **paramswrapper:** use K8s jenkins-inbound-agent by default [SRE-30] (76e996b369d949d39f5da0d7095780cd70d34819)
- **paramswrapper:** display BUILD_NOCACHE for all build types [SRE-30] (41dd841efded88b4828883535e86f4901000afd9)
- **paramswrapper:** DEPENDENCIES set to false by default [SRE-30] (fcc46a0ed3ff9138e4a3fce5982b4140c54eed6b)
- **gcputils:** added initial version (3e99e8c2faac58beaf5573fe5e1bc2d3e0cc7242)
- **dockerutils:** use gcpUtils.infraProject var instead of kefirinfra (eed2e141fc1557746516c8ba7131e59ea3a5f41c)
- **helmutils:** use gcpUtils.infraProject var instead of kefirinfra (3fe71bf6143991342aba0a52073b4572c85128d2)
- **paramswrapper:** use gcpUtils.devProject variable instead of kefirdev (2bd46328aef121097594eab5eee866f76066f559)
- **gitlabutils:** added credentialsId property (8345db330364fc54dba5b186d5833e822a372dcb)
- **scmutils:** retrieve credentialsId using gitlabUtils (62645ae3bb274518dadfaf954df77d6fbc92cbb8)
- **paramswrapper:** replaced confidential data with variables (5288ff6d01d09013fcca70095a4668208ff7569b)
- **paramswrapper:** added sortMode for SHARED_LIB_BRANCH parameter (35d73a2e0aeb5323b2b7d06397a5d342920ca959)
- **envutils:** removed gcloud info call during init (85a935cb50ff8931f818084d81c73753a896c30d)
- **dockerutils:** labeled scripted calls properly (acdd4d26c3359f0cd63147ac4193e20de2578286)
- **envutils:** labeled scripted calls properly (00837e515ef3276617a5d9a164fc3190d2c71ad8)
- **helmutils:** labeled scripted calls properly (258c0faf7951965dc5b3d70e6976e7e8686fbbc5)
- **scmutils:** labeled scripted calls properly (a54b78adf2285df4a9c90492051df45889d449d5)
- **envutils:** setup workspace environment conditionally (66237cc911f7fcb7d75c55c9577726eb973aa99d)
- **gitlabutils:** added gitSshUrlInternal and repoSshUrlInternal props (0feaee842bf6871548a7f15dea1bca8d7e3d24a2)
- **scmutils:** added comparison with repoSshUrlInternal in getGitBranches (9d64137ca49aa40e2e9afdd0adab98d0f6b531d2)
- **gcputils:** added devProject property (449975db09536bd981e10994f1df471aaa83aa64)
- **paramswrapper:** added SLACK_DISABLE* parameters (30054e19ac7a5dbbdd5a649fc9b755233fd6ff6c)
- **slackutils:** added conditions for SLACK_DISABLE* parameters (d3ef57a55ebd803e9a1e3f0e8a8da27d1d3e3f48)
- **paramswrapper:** set default value of SHARED_LIB_BRANCH to master (becd023ab064950b2e5207432ad7c71128a0386a)

## 0.0.2
2021-06-08

### Fixes

- **dockerutils:** call sh and bat for cleanup properly (baaefde57779dd4e7f450bc112a58399e9d16a76)
- **scmutils:** added GIT_TAG_REFSPEC [SRE-211] (00fec7813ebe215b82927a562299b2720aeea2f0)
- **docketutils:** allow to set imageTags directly [SRE-211] (c425f743b960ff4762f24c007e34f7d252dddca9)
- **dockerutils:** removed DEPLOY condition from image delivery [SRE-211] (b3c0576c585fa2375c5b4aa2190492cba59abce7)
- **paramswrapper:** added IMAGE_TAG_PARAMETERS variable [SRE-211] (8c53f17a5e6490fa9f8e68e0204ccc0c1c2324e7)
- **scmutils:** wrapped retrieving of GIT_TAG_REFSPEC with try/catch [SRE-211] (312bf438d9ccb49711380fb8ffbd146ba7b07f27)
- **scmutils:** moved addExtensions() from paramsWrapper [SRE-211] (dae9bdb2bea9046dcfde1f208bdf526ff5f0bfe3)
- **paramswrapper:** made DEPENDENCIES parameter conditional [SRE-211] (1436405223e1fa29c5c25c5068d3637aa1f4b57a)
- **scmutils:** added stripGitRefspecPrefixes() [SRE-211] (3da1ee62a56d394c23f36525a3942422dda13f0b)
- **scmutils:** retrieve GIT branch for GitLab-triggered jobs [SRE-211] (03fa3fadfbc84ede8cd046a9cfd95502d866418b)
- **paramswrapper:** properly provision default parameters [SRE-211] (add781dca5b616344d207b1f0802cab1c34eed35)

## 0.0.1
2021-06-05

### Fixes

- **jenkins-shared-libraries:** Initial commit (5ab5b0363aef5f88a1f173d8ec9391131937444c)
- **paramswrapper:** made DEPLOY parameter conditional [SRE-30] (db389d0857172b2c6476b9d73971930722663115)
- **slackutils:** use containerImages instead of helmVars [SRE-30] (10c85032820c1ab235bbab2df7d7bb647ac342b2)
- **slackutils:** fix presence detection of DEPLOYMENT in parameters (3dc1425eb2af11af7ba5fcd666be9533dc108e03)
- **paramswrapper:** strip unnecessary GIT prefixes from SHARED_LIB_BRANCH (0f27a2ff253a77e3b2cdc6f24f036a325e02423c)
- **paramswrapper:** set type of SHARED_LIB_BRANCH to listGitBranches (a178ce1d4a875544137498fbbb8576ef03bf9dba)