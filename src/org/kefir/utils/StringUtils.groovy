package org.kefir.utils

class StringUtils {
    static def base64Encode(inputString){
        def encoded = inputString.bytes.encodeBase64().toString()
        return encoded
    }

    static def base64Decode(encodedString){
        byte[] decoded = encodedString.decodeBase64()
        String decode = new String(decoded)
        return decode
    }

    static String pathFinder(encodedPath){
        String path = encodedPath.split('/').find { it.contains('%2F') }
        return path
    }

    static List<Integer> parseVersionString(versionInput) {
        if (versionInput instanceof List) {
            return versionInput.collect { it.toInteger() }
        }

        String versionString = versionInput.toString()

        if (versionString.startsWith('(') && versionString.endsWith(')')) {
            versionString = versionString[1..-2]
            return versionString.split(',').collect { it.trim().toInteger() }
        }

        String cleanVersion = versionString.replaceAll(/^[vV]/, '').split('-')[0]
        return cleanVersion.split(/[.,]/).collect { it.trim().toInteger() }
    }

    static Boolean isVersionGreater(List<Integer> versionA, List<Integer> versionB) {
        for (int i = 0; i < 3; i++) {
            if (versionA[i] > versionB[i]) return true
            if (versionA[i] < versionB[i]) return false
        }
        return false
    }
}
