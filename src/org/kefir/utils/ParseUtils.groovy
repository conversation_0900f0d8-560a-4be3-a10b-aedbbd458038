package org.kefir.utils

class ParseUtils {
    static def parseBlInfo(String fileContent) {
        if (!fileContent) {
            return [:]
        }

        def commentedPattern = /^\s*#.*bl_info\s*=/
        if (fileContent.split('\n').any { line -> line =~ commentedPattern }) {
            return [:]
        }

        def blInfoMap = [:]
        def blInfoPattern = /bl_info\s*=\s*\{(?s)(.+?)\}/
        def blInfoMatcher = (fileContent =~ blInfoPattern)

        if (blInfoMatcher.find()) {
            def blInfoContent = blInfoMatcher.group(1)

            def keyValuePattern = /"([^"]+)"\s*:\s*(?:\((.*?)\)|"(.*?)"|([^,\n]+))\s*(?:,|$)/
            def keyValueMatcher = (blInfoContent =~ keyValuePattern)

            keyValueMatcher.each { match ->
                try {
                    def key = match[1]
                    def value

                    // Обработка кортежей (версий) - сохраняем как строку в скобках
                    if (match[2] != null) {
                        value = "(${match[2]})"
                    }
                    // Обработка строк в кавычках
                    else if (match[3] != null) {
                        value = match[3]
                    }
                    // Обработка простых значений
                    else {
                        value = match[4].trim()
                        if (value == 'True') {
                            value = true
                        } else if (value == 'False') {
                            value = false
                        } else if (value ==~ /^-?\d+$/) {
                            value = value.toInteger()
                        } else if (value ==~ /^-?\d+\.\d+$/) {
                            value = value.toFloat()
                        }
                    }

                    blInfoMap[key] = value
                } catch (Exception e) {
                    println "Ошибка при обработке: ${match} - ${e.message}"
                }
            }
        }

        return blInfoMap
    }
}
