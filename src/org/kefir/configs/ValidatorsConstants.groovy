package org.kefir.configs

class ValidatorsConstants {
    static final String PACKAGE_JSON_SEM_VER_PATTERN = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/
    static final String PACKAGE_JSON_NAME_PREFIX = "com.kefir."
    static final String PACKAGE_JSON_REPOSITORY_TYPE = "git"

    static final String INIT_PY_SEM_VER_PATTERN = /\(\s*(\d+),\s*(\d+),\s*(\d+)\)/
}
