package org.kefir.services

import org.kefir.configs.MattermostConstants

class MattermostNotificationService {
    private final def script
    private final GitlabService gitlab
    
    MattermostNotificationService(script) {
        this.script = script
        this.gitlab = new GitlabService(script)
    }
    
    /**
     * Sends a notification about successful package publication
     * @param config Configuration map containing notification settings
     * @return Map with success status and message
     */
    Map sendPackagePublishedNotification(Map config) {
        try {
            def packageInfo = getPackageInfo(config.branch ?: script.env.BRANCH_NAME)
            script.echo("packageInfo: ${packageInfo}")
            def changelogEntry = getChangelogEntry(packageInfo.version, config.branch ?: script.env.BRANCH_NAME)

            script.echo("changelogEntry2: ${changelogEntry}")
            
            String message = buildPackageNotificationMessage(packageInfo, changelogEntry)
            
            def result = script.mattermostSend(
                channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
                color: MattermostConstants.COLOR_SUCCESS,
                message: message
            )
            
            return [success: true, log: "Notification sent successfully to ${config.channel ?: MattermostConstants.DEFAULT_CHANNEL}"]
        } catch (Exception e) {
            script.echo("Failed to send Mattermost notification: ${e.message}")
            return [success: false, log: "Failed to send notification: ${e.message}"]
        }
    }
    
    /**
     * Sends a notification about failed build
     * @param config Configuration map containing notification settings
     * @param error Error information
     * @return Map with success status and message
     */
    Map sendBuildFailedNotification(Map config, Map error = [:]) {
        try {
            def packageInfo = getPackageInfo(config.branch ?: script.env.BRANCH_NAME)
            
            String message = buildFailureNotificationMessage(packageInfo, error)
            
            def result = script.mattermostSend(
                channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
                color: MattermostConstants.COLOR_FAILURE,
                message: message
            )
            
            return [success: true, log: "Failure notification sent successfully"]
        } catch (Exception e) {
            script.echo("Failed to send failure notification: ${e.message}")
            return [success: false, log: "Failed to send failure notification: ${e.message}"]
        }
    }
    
    /**
     * Extracts package information from package.json
     * @param branch Branch to read from
     * @return Map containing package name and version
     */
    private Map getPackageInfo(String branch) {
        try {
            String projectId = gitlab.getProjectIdByJobName()
            def packageJson = gitlab.getFileJsonOutputFromRemote(projectId, 'package.json', branch)
            
            return [
                name: packageJson.name ?: 'Unknown Package',
                version: packageJson.version ?: 'Unknown Version',
                displayName: packageJson.displayName ?: packageJson.name ?: 'Unknown Package'
            ]
        } catch (Exception e) {
            script.echo("Warning: Could not extract package info: ${e.message}")
            return [
                name: 'Unknown Package',
                version: 'Unknown Version',
                displayName: 'Unknown Package'
            ]
        }
    }
    
    /**
     * Extracts changelog entry for specific version
     * @param version Version to extract changelog for
     * @param branch Branch to read from
     * @return String containing changelog entry or empty string
     */
    private String getChangelogEntry(String version, String branch) {
        try {
            String projectId = gitlab.getProjectIdByJobName()
            String changelogContent = gitlab.getFileOutputFromRemote(projectId, MattermostConstants.CHANGELOG_FILENAME, branch)

            return parseChangelogForVersion(changelogContent, version)
        } catch (Exception e) {
            script.echo("Warning: Could not extract changelog: ${e.message}")
            return ""
        }
    }
    
    /**
     * Parses changelog content to extract specific version entry
     * @param changelogContent Full changelog content
     * @param version Version to extract
     * @return String containing version-specific changelog
     */
    private String parseChangelogForVersion(String changelogContent, String version) {
        script.echo("Changelog content: ${changelogContent}")
        if (!changelogContent || !version) {
            return ""
        }
        
        try {
            def lines = changelogContent.split('\n')
            def versionLines = []
            boolean inVersionSection = false
            boolean foundVersion = false
            
            for (String line : lines) {
                if (line.trim().startsWith('##') && line.contains(version)) {
                    script.echo("Found version: ${version}")
                    inVersionSection = true
                    foundVersion = true
                    continue
                }
                
                if (inVersionSection && line.trim().startsWith('##') && !line.contains(version)) {
                    break
                }
                
                if (inVersionSection) {
                    if (versionLines.isEmpty() && line.trim().isEmpty()) {
                        continue
                    }
                    versionLines.add(line)
                }
            }
            
            if (!foundVersion) {
                script.echo("JOPA")
                return ""
            }
            
            while (versionLines.size() > 0 && versionLines.last().trim().isEmpty()) {
                versionLines.removeLast()
            }

            script.echo("versionLines: $versionLines")
            
            return versionLines.join('\n').trim()
        } catch (Exception e) {
            script.echo("Warning: Error parsing changelog: ${e.message}")
            return ""
        }
    }
    
    /**
     * Builds notification message for successful package publication
     * @param packageInfo Package information
     * @param changelogEntry Changelog entry for the version
     * @return Formatted notification message
     */
    private String buildPackageNotificationMessage(Map packageInfo, String changelogEntry) {
        StringBuilder message = new StringBuilder()

        message.append(String.format(MattermostConstants.PACKAGE_PUBLISHED_TITLE, packageInfo.displayName, packageInfo.version))
        message.append("\n\n")
        script.echo("Changelog entry: ${changelogEntry}")

        if (changelogEntry && !changelogEntry.trim().isEmpty()) {
            String truncatedChangelog = changelogEntry.length() > MattermostConstants.MAX_CHANGELOG_LENGTH ?
                changelogEntry.substring(0, MattermostConstants.MAX_CHANGELOG_LENGTH) + "..." :
                changelogEntry

            message.append("**Changelog:**\n")
            message.append("```\n")
            message.append(truncatedChangelog)
            message.append("\n```\n\n")
        }
        
        message.append("**Build Information:**\n")
        message.append("• Job: [${script.env.JOB_NAME}](${script.env.JOB_URL})\n")
        message.append("• Build: [${script.env.BUILD_DISPLAY_NAME}](${script.env.BUILD_URL})\n")
        
        if (script.env.GIT_BRANCH) {
            message.append("• Branch: ${script.env.GIT_BRANCH}\n")
        }
        
        return message.toString()
    }
    
    /**
     * Builds notification message for build failure
     * @param packageInfo Package information
     * @param error Error information
     * @return Formatted failure notification message
     */
    private String buildFailureNotificationMessage(Map packageInfo, Map error) {
        StringBuilder message = new StringBuilder()

        message.append(String.format(MattermostConstants.BUILD_FAILED_TITLE, packageInfo.displayName))
        message.append("\n\n")
        
        if (error.message) {
            message.append("**Error:** ${error.message}\n\n")
        }
        
        message.append("**Build Information:**\n")
        message.append("• Job: [${script.env.JOB_NAME}](${script.env.JOB_URL})\n")
        message.append("• Build: [${script.env.BUILD_DISPLAY_NAME}](${script.env.BUILD_URL})\n")
        
        if (script.env.GIT_BRANCH) {
            message.append("• Branch: ${script.env.GIT_BRANCH}\n")
        }
        
        return message.toString()
    }
}
