package org.kefir.services

import org.kefir.configs.ConfluenceConstants
import org.kefir.configs.DockerConstants

class DocBuilder implements Serializable {
    private final def script

    DocBuilder(script) {
        this.script = script
    }
    
    def publishDocs(Map config) {
        script.echo """
        📄 Параметры публикации документации:
            - Имя пространства к Confluence: ${config.confluenceSpaceName}
            - ID родительской страницы в Confluence: ${config.confluenceParentPageID}
            - Метка документации: ${config.documentationLabel}
            - Путь до директории с документацией: ${script.WORKSPACE}/${config.documentationDirectoryPath}
            - Заголовок документации: ${config.documentationTitle}
            - Путь до файла с домашней страницей документации: ${script.WORKSPACE}/${config.documentationHomePagePath}
        """.stripIndent()
        
        script.withCredentials([
            script.string(credentialsId: ConfluenceConstants.CONFLUENCE_CREDENTIALS_ID, variable: 'CONFLUENCE_TOKEN')
        ]) {
            script.docker.image(DockerConstants.DOCS_IMAGE).inside(
                "--user root --entrypoint=\"\" " +
                "-e CONFLUENCE_URL='${ConfluenceConstants.CONFLUENCE_INT_URL}' " +
                "-e CONFLUENCE_SPACE='${config.confluenceSpaceName}' " +
                "-e CONFLUENCE_PARENT_PAGE='${config.confluenceParentPageID}' " +
                "-e DOCUMENTATION_ROOT='${script.WORKSPACE}/${config.documentationDirectoryPath}' " +
                "-e DOCUMENTATION_LABEL='${config.documentationLabel}' " +
                "-e DOCUMENTATION_TITLE='${config.documentationTitle}' " +
                "-e DOCUMENTATION_HOME='${script.WORKSPACE}/${config.documentationHomePagePath}'"
            ) {
                script.sh 'cd /opt/md2cf && poetry run python main.py'
            }
        }
        
        script.echo "✅ Документация успешно опубликована в пространстве ${config.confluenceSpaceName}"
    }
}