package org.kefir.services

import groovy.json.JsonOutput
import org.kefir.configs.GitlabConstants
import org.kefir.utils.StringUtils

class GitlabService implements Serializable {
    private final def script

    GitlabService(script) {
        this.script = script
    }

    void downloadFile(String projectId, String path, String branch) {
        Map tempFile = [
                label: 'Create tmp file for raw git file',
                script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"',
                returnStdout: true
        ]

        tempFilename = script.sh(tempFile).trim()
        String URL = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/files/${path.replace('/', '%2F')}/?ref=${scmUtils.stripGitRefspecPrefixes(branch)}"

        withCredentials([string(credentialsId: "${GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID}", variable: 'TOKEN')]) {
            script.sh("curl --insecure --header \"Private-Token: \"\$TOKEN\"\" \"${URL}\" > ${tempFilename}")
            output = script.readJSON(file: tempFilename)
            outputEnc = script.sh(script: "echo ${output.content} | base64 --decode", returnStdout: true)
            script.writeFile(file: output.fileName, text: outputEnc)
            script.sh("rm -f ${tempFilename}")
        }
    }

    void downloadTreeFile(String projectId, String path, String branch) {
        Map tempFile = [
                label: 'Create tmp file for raw git file',
                script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.json"',
                returnStdout: true
        ]

        tempTreeFilename = script.sh(tempFile).trim()
        // Restriction: 100 files as per_page=100 (use downloadRepoFolderAsArchive instead)
        String apiTreeUrl = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/tree?ref=${scmUtils.stripGitRefspecPrefixes(branch)}&path=${path}&recursive=true&per_page=100"

        withCredentials([string(credentialsId: "${GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID}", variable: 'TOKEN')]) {
            script.sh("curl --insecure --header \"Private-Token: \"\$TOKEN\"\" \"${apiTreeUrl}\" > ${tempTreeFilename}")
            outputTree = script.readJSON(file: tempTreeFilename)
            outputTree.each{elem ->
                if(elem['type'] != 'tree'){
                    tempFilename = script.sh(tempFile).trim()
                    String API_FILE_URL = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/files/${elem['path'].replace('/', '%2F')}/?ref=${scmUtils.stripGitRefspecPrefixes(branch)}"
                    withCredentials([string(credentialsId: "${GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID}", variable: 'TOKEN')]) {
                        script.sh("curl --insecure --header \"Private-Token: \"\$TOKEN\"\" \"${API_FILE_URL}\" > ${tempFilename}")
                    }
                    output = script.readJSON(file: tempFilename)
                    dataDir = script.sh(returnStdout: true, script:""" echo "${elem['path']}" | sed  -e "s/${output.fileName}//g" """)
                    outputEnc = script.sh(script: "echo ${output.content} | base64 --decode", returnStdout: true)
                    script.writeFile(file: output.fileName, text: outputEnc)
                    script.sh("mkdir -p ${dataDir}")
                    script.sh("mv ${output.fileName} ${dataDir}")
                    script.sh("rm -f ${tempFilename}")
                }
            }
        }
        script.sh("rm -f ${tempTreeFilename}")
    }

    void downloadRepoFolderAsArchive(String projectId, String commitSha, String repoRelativePath) {
        String tempFilename = script.sh(script: 'mktemp -u "$WORKSPACE/raw_output.XXXXXXXXXX.tar"', returnStdout: true).trim()
        String apiTreeUrl = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/archive.tar?sha=${commitSha}&path=${repoRelativePath}"

        String responseContent = getHTTPResponse(apiTreeUrl)

        script.sh("echo '${responseContent}' > ${tempFilename}")
        script.sh("tar -xf ${tempFilename} --strip-components=1")
        script.sh("rm -rf ${tempFilename}")
    }

    String getBranchLastCommitSha(String projectId, String branch, String commitType = 'full') {
        String commitSha = ''
        String apiTreeUrl = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/branches/${branch.replace('/', '%2F')}"

        String responseContent = getHTTPResponse(apiTreeUrl)
        def output = script.readJSON(text: responseContent)

        if (commitType == 'full') {
            commitSha = output['commit']['id']
        } else if (commitType == 'short') {
            commitSha = output['commit']['short_id']
        }

        return commitSha
    }

    String getCommitShaByBranchOrTag(String projectId, String branchOrTag, String commitType = 'full') {
        String commitSha = ''
        String apiTreeUrl = "https://${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/commits/${branchOrTag.replace('/', '%2F')}"

        String responseContent = getHTTPResponse(apiTreeUrl)
        def output = script.readJSON(text: responseContent)

        if (commitType == 'full') {
            commitSha = output['id']
        } else if (commitType == 'short') {
            commitSha = output['short_id']
        }

        return commitSha
    }

    String getHTTPResponse(String url) {
        try {
            script.withCredentials([script.string(credentialsId: "${GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID}", variable: 'GITLAB_API_TOKEN')]) {
                def response = script.httpRequest(
                        url: url,
                        customHeaders: [[name: 'PRIVATE-TOKEN', value: script.env.GITLAB_API_TOKEN]],
                        validResponseCodes: '200'
                )
                return response.content
            }
        }
        catch (Exception e) {
            script.error("HTTP request failed for URL: ${url}\nError:\n${e}")
        }
    }

    String getProjectIdByJobName() {
        try {
            String encodedPath = StringUtils.pathFinder(script.env.JOB_NAME)

            if (encodedPath == null) {
                script.error "No GitLab repo path found in JOB_NAME: ${script.env.JOB_NAME}"
            }

            script.echo "Extracted GitLab repo path: ${encodedPath}"

            String responseContent = getHTTPResponse("${GitlabConstants.GITLAB_API_URL}/projects/${encodedPath}")

            def output = script.readJSON(text: responseContent)

            return output.id.toString()
        } catch (Exception e) {
            script.error("Failed to get GitLab project ID: ${e.message}")
        }
    }

    String getProjectNameByJobName() {
        try {
            String encodedPath = StringUtils.pathFinder(script.env.JOB_NAME)

            if (encodedPath == null) {
                script.error "No GitLab repo path found in JOB_NAME: ${script.env.JOB_NAME}"
            }

            String repoName = encodedPath.split('%2F').last()

            script.echo "Extracted GitLab repo name: ${repoName}"

            return repoName
        } catch (Exception e) {
            script.error("Failed to get GitLab project name: ${e.message}")
        }
    }

    def getFileJsonOutputFromRemote(String projectId, String filePath, String branch) {
        try {
            String URL = "${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/files/${filePath.replace('/', '%2F')}/?ref=${script.scmUtils.stripGitRefspecPrefixes(branch)}"

            String response = getHTTPResponse(URL)
            def outputJson = script.readJSON(text: response)
            def outputContent = outputJson.content
            String outputEnc = StringUtils.base64Decode(outputContent)
            def jsonOutput = script.readJSON(text: outputEnc)

            return jsonOutput
        } catch (Exception e) {
            script.error("Файл ${filePath} не найден, или не является Json\nПодробная ошибка:\n${e}")
        }
    }
    
    String getFileOutputFromRemote(String projectId, String filePath, String branch) {
        try {
            String URL = "${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/files/${filePath.replace('/', '%2F')}/?ref=${script.scmUtils.stripGitRefspecPrefixes(branch)}"

            String response = getHTTPResponse(URL)
            def outputJson = script.readJSON(text: response)
            def outputContent = outputJson.content
            String outputEnc = StringUtils.base64Decode(outputContent)

            return outputEnc
        } catch (Exception e) {
            script.error("Файл ${filePath} не найден, или его не получилось прочитать\nПодробная ошибка:\n${e}")
        }
    }

    boolean checkFileExists(String projectId, String filePath, String branch) {
        try {
            String URL = "${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/repository/files/${filePath.replace('/', '%2F')}/?ref=${script.scmUtils.stripGitRefspecPrefixes(branch)}"

            script.withCredentials([script.string(credentialsId: "${GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID}", variable: 'GITLAB_API_TOKEN')]) {
                def response = script.httpRequest(
                        url: URL,
                        customHeaders: [[name: 'PRIVATE-TOKEN', value: script.env.GITLAB_API_TOKEN]],
                        validResponseCodes: '200,404'
                )
                return response.status == 200
            }
        } catch (Exception e) {
            script.echo("Ошибка при проверке существования файла ${filePath} в ветке ${branch}: ${e.message}")
            return false
        }
    }

    void addMergeRequestComment(String comment) {
        script.withCredentials([
                script.string(
                        credentialsId: GitlabConstants.GITLAB_API_TOKEN_CREDENTIALS_ID,
                        variable: 'GITLAB_API_TOKEN'
                )
        ]) {
            try {
                def projectId = getProjectIdByJobName()

                if (!script.env.CHANGE_ID) {
                    script.echo "Сборка не является запросом на слияние.\nКомментарий не оставлен."
                    return
                }

                script.httpRequest(
                        acceptType: 'APPLICATION_JSON',
                        contentType: 'APPLICATION_JSON',
                        httpMode: 'POST',
                        requestBody: JsonOutput.toJson([body: comment]),
                        url: "${GitlabConstants.GITLAB_API_URL}/projects/${projectId}/merge_requests/${script.env.CHANGE_ID}/notes",
                        customHeaders: [[name: 'PRIVATE-TOKEN', value: script.env.GITLAB_API_TOKEN]],
                        quiet: true
                )
                script.echo "✅ Комментарий оставлен в MR-${script.env.CHANGE_ID}"
            } catch (Exception e) {
                script.error("Не удалось оставить комментарий в MR-${script.env.CHANGE_ID}.\nОшибка:\n${e.message}")
            }
        }
    }
}
