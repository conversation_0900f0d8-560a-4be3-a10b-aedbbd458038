package org.kefir.services

class FileExistenceService implements Serializable {
    private final Script script
    private final GitlabService gitlab

    FileExistenceService(Script script) {
        this.script = script
        this.gitlab = new GitlabService(script)
    }

    Map checkFileExistenceInBranches(String projectId, String filePath, String currentBranch, String targetBranch) {
        def results = [:]

        boolean existsInCurrent = gitlab.checkFileExists(projectId, filePath, currentBranch)
        results.currentBranch = [
            branch: currentBranch,
            exists: existsInCurrent,
            message: existsInCurrent ?
                "Файл ${filePath} найден в ветке ${currentBranch}" :
                "Файл ${filePath} не найден в ветке ${currentBranch}"
        ]

        boolean existsInTarget = gitlab.checkFileExists(projectId, filePath, targetBranch)
        results.targetBranch = [
            branch: targetBranch,
            exists: existsInTarget,
            message: existsInTarget ?
                "Файл ${filePath} найден в ветке ${targetBranch}" :
                "Файл ${filePath} не найден в ветке ${targetBranch}"
        ]

        results.success = existsInCurrent
        results.log = buildValidationMessage(filePath, results.currentBranch, results.targetBranch)

        return results
    }

    private String buildValidationMessage(String filePath, Map currentResult, Map targetResult) {
        def messages = []
        
        if (!currentResult.exists) {
            messages << "Файл ${filePath} отсутствует в текущей ветке ${currentResult.branch}"
        } else if (!targetResult.exists) {
            messages << "Файл ${filePath} найден в текущей ветке ${currentResult.branch}, но отсутствует в целевой ветке ${targetResult.branch}"
        } else {
            messages << "Файл ${filePath} найден в обеих ветках"
        }
        
        return messages.join('\n')
    }
}
