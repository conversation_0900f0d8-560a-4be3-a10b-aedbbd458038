package org.kefir.services

class NpmService {
    private final def script

    NpmService(script) {
        this.script = script
    }

    void publishNpm(Map config) {
        script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
            script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                script.sh('npm config fix && npm publish')
            }
            return [success: true, log: "Пакет загружен в ${config.verdaccioInstance}"]
        }
    }

    void unpublishNpm(Map config) {
        script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
            script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                script.sh("npm config fix && npm unpublish --force ${config.packageName}")
            }
            return [success: true, log: "Пакет ${config.packageName} удален из ${config.verdaccioInstance}"]
        }
    }
}
