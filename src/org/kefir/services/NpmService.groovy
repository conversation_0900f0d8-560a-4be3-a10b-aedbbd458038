package org.kefir.services

class NpmService {
    private final def script

    NpmService(script) {
        this.script = script
    }

    Map publishNpm(Map config) {
        try {
            // Get package info before publishing
            def packageInfo = getPackageInfo()

            script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
                script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                    script.sh('npm config fix && npm publish')
                }
            }

            return [
                success: true,
                log: "Пакет ${packageInfo.name}@${packageInfo.version} загружен в ${config.verdaccioInstance}",
                packageInfo: packageInfo
            ]
        } catch (Exception e) {
            return [
                success: false,
                log: "Ошибка при публикации пакета: ${e.message}",
                error: e.message
            ]
        }
    }

    /**
     * Extracts package information from local package.json
     * @return Map containing package name, version, and other details
     */
    Map getPackageInfo() {
        try {
            def packageJson = script.readJSON(file: 'package.json')
            return [
                name: packageJson.name ?: 'Unknown Package',
                version: packageJson.version ?: 'Unknown Version',
                displayName: packageJson.displayName ?: packageJson.name ?: 'Unknown Package',
                author: packageJson.author ?: 'Unknown Author',
                description: packageJson.description ?: ''
            ]
        } catch (Exception e) {
            script.echo("Warning: Could not read package.json: ${e.message}")
            return [
                name: 'Unknown Package',
                version: 'Unknown Version',
                displayName: 'Unknown Package',
                author: 'Unknown Author',
                description: ''
            ]
        }
    }

    void unpublishNpm(Map config) {
        script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
            script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                script.sh("npm config fix && npm unpublish --force ${config.packageName}")
            }
            return [success: true, log: "Пакет ${config.packageName} удален из ${config.verdaccioInstance}"]
        }
    }
}
