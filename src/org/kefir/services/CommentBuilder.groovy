package org.kefir.services

import org.kefir.configs.GitlabConstants

class CommentBuilder {
    private final Script script
    private final Map<String, String> sectionTitles = [
            'packageJson': '📦 Проверка package.json',
            'initPy': '🐍 Проверка __init__.py',
            'other': '⚙️ Другие проверки'
    ]

    CommentBuilder(Script script) {
        this.script = script
    }

    String buildSection(String validatorType, List<Map> results) {
        if (!results) return ''

        def section = "\n### ${sectionTitles[validatorType] ?: "⚙️ ${validatorType.capitalize()}"}\n"
        results.each { result ->
            def emoji = result.success ? "✅" : "❌"
            section += "- ${emoji} ${result.log}\n"
        }
        return section
    }

    static String buildSummary(List<Map> allResults) {
        def successCount = allResults.count { it.success }
        def totalCount = allResults.size()

        return """
            
### 📊 Итоги
- Успешных проверок: ${successCount}
- Неуспешных проверок: ${totalCount - successCount}
- Общее количество: ${totalCount}
            
${successCount == totalCount ? '🎉 Все проверки пройдены успешно!' : '⚠️ Требуется исправление ошибок!'}
"""
    }

    String buildFullComment(List<Map> validationResults) {
        def groupedResults = validationResults.groupBy { it.validatorType ?: 'other' }
        def comment = new StringBuilder()

        ['packageJson', 'initPy', 'other'].each { type ->
            comment << buildSection(type, groupedResults[type])
        }

        comment << buildSummary(validationResults)

        return comment.toString()
    }
}
