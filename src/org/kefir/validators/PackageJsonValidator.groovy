package org.kefir.validators

import org.kefir.configs.GitlabConstants
import org.kefir.configs.ValidatorsConstants
import org.kefir.services.GitlabService
import org.kefir.services.NpmService
import org.kefir.services.FileExistenceService
import org.kefir.utils.StringUtils

class PackageJsonValidator extends BaseValidator {
    private final GitlabService gitlab
    private final NpmService npmService
    private final FileExistenceService fileExistenceService
    private String projectId
    private String projectName
    private Map packageJsonContent
    private Map packageJsonTargetContent
    private boolean initialized = false

    PackageJsonValidator(Script script) {
        super(script)
        this.gitlab = new GitlabService(script)
        this.npmService = new NpmService(script)
        this.fileExistenceService = new FileExistenceService(script)
    }

    private void ensureInitialized() {
        if (!initialized) {
            try {
                this.projectId = gitlab.getProjectIdByJobName()
                this.projectName = gitlab.getProjectNameByJobName()
                this.packageJsonContent = gitlab.getFileJsonOutputFromRemote(projectId, 'package.json', script.env.CHANGE_BRANCH) ?: [:]

                try {
                    this.packageJsonTargetContent = gitlab.getFileJsonOutputFromRemote(projectId, 'package.json', script.env.CHANGE_TARGET) ?: [:]
                } catch (Exception targetException) {
                    script.echo("Файл package.json не найден в целевой ветке ${script.env.CHANGE_TARGET}")
                    this.packageJsonTargetContent = [:]
                }

                initialized = true
            } catch (Exception e) {
                script.error("Ошибка инициализации PackageJsonValidator: ${e.message}")
                throw e
            }
        }
    }

    Map checkName() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("name")) {
            return createResult(false, "Поле name в package.json не найдено")
        }

        String packageName = packageJsonContent["name"]?.toString()?.trim()
        if (packageName.isEmpty()) {
            return createResult(false, "Поле name в package.json не заполнено")
        }

        if (!packageName.startsWith(ValidatorsConstants.PACKAGE_JSON_NAME_PREFIX)) {
            return createResult(false, "Значение поля name должно начинаться с ${ValidatorsConstants.PACKAGE_JSON_NAME_PREFIX}")
        }

        if (!packageName.endsWith(projectName)) {
            return createResult(false, "Значение поля name должно заканчиваться названием репозитория: ${projectName}")
        }

        def parts = packageName.tokenize('.')
        for (part in parts) {
            if (!(part ==~ /^[a-z0-9-]+$/)) {
                return createResult(false, "Значение поля name должно быть в формате kebab-case")
            }

            if (part.startsWith("-") || part.endsWith("-")) {
                return createResult(false, "Значение поля name не должно начинаться или заканчиваться дефисом")
            }

            if (part.contains("--")) {
                return createResult(false, "Значение поля name не должно содержать несколько дефисов подряд")
            }
        }

        return createResult(true, "Поле name валидно: ${packageName}")
    }

    Map checkVersion() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("version")) {
            return createResult(false, "Поле version в package.json не найдено")
        }

        String version = packageJsonContent["version"]?.toString()?.trim()

        if (version.isEmpty()) {
            return createResult(false, "Поле version в package.json не заполнено")
        }

        if (!(version ==~ ValidatorsConstants.PACKAGE_JSON_SEM_VER_PATTERN)) {
            return createResult(false, "Версия ${version} не соответствует SemVer")
        }


        if (!packageJsonTargetContent.isEmpty()) {
            String targetVersion = packageJsonTargetContent["version"]?.toString()?.trim()
            def listVersion = StringUtils.parseVersionString(version)
            def listTargetVersion = StringUtils.parseVersionString(targetVersion)

            if (!StringUtils.isVersionGreater(listVersion, listTargetVersion)) {
                return createResult(false, "Версия ${version} должна быть больше чем ${targetVersion} в ветке ${script.env.CHANGE_TARGET}")
            }
        } else {
            return createResult(true, "Версия ${version} валидна, но файл отсутствует в целевой ветке ${script.env.CHANGE_TARGET} - сравнивать не с чем")
        }

        return createResult(true, "Версия ${version} валидна")
    }

    Map checkAuthor() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("author")) {
            return createResult(false, "Поле author в package.json не найдено")
        }

        def author = packageJsonContent["author"]
        if (author == null) {
            return createResult(false, "Поле author в package.json не заполнено")
        }

        if (author instanceof Map && !author.containsKey("name")) {
            return createResult(false, "Поле name объекта author не найдено")
        }

        String authorName = (author instanceof Map) ? author["name"]?.toString()?.trim() : author.toString().trim()
        if (authorName.isEmpty()) {
            return createResult(false, "Поле name объекта author не заполнено")
        }

        return createResult(true, "Поле author валидно: ${authorName}")
    }

    Map checkRepositoryUrl() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("repository")) {
            return createResult(false, "Поле repository в package.json не найдено")
        }

        def repository = packageJsonContent["repository"]
        if (repository == null) {
            return createResult(false, "Поле repository в package.json не заполнено")
        }

        String repoPath = StringUtils.pathFinder(script.env.JOB_NAME)
        String encodedRepoPath = repoPath.replaceAll('%2F', '/')
        String expectedUrl = "git@${GitlabConstants.GITLAB_DOMAIN_NAME}:${encodedRepoPath}.git"

        if (repository instanceof String) {
            if (repository != expectedUrl) {
                return createResult(false, "URL репозитория должен быть: ${expectedUrl}")
            }
            return createResult(true, "URL репозитория валиден")
        }

        if (!repository.containsKey("url")) {
            return createResult(false, "Поле url в repository не найдено")
        }

        String repositoryUrl = repository["url"]?.toString()?.trim()
        if (repositoryUrl.isEmpty()) {
            return createResult(false, "Поле url в repository не заполнено")
        }

        if (repositoryUrl != expectedUrl) {
            return createResult(false, "URL репозитория должен быть: ${expectedUrl}")
        }

        if (!repository.containsKey("type")) {
            return createResult(false, "Поле type в repository не найдено")
        }

        String repositoryType = repository["type"]?.toString()?.trim()
        if (repositoryType.isEmpty()) {
            return createResult(false, "Поле type в repository не заполнено")
        }

        if (repositoryType != ValidatorsConstants.PACKAGE_JSON_REPOSITORY_TYPE) {
            return createResult(false, "Тип репозитория должен быть: ${ValidatorsConstants.PACKAGE_JSON_REPOSITORY_TYPE}")
        }

        if (repository.containsKey("revision") && !repository["revision"].toString().trim().isEmpty()) {
            return createResult(false, "Поле revision должно быть пустым")
        }

        return createResult(true, "Поле repository валидно")
    }

    Map checkDisplayName() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("displayName")) {
            return createResult(false, "Поле displayName в package.json не найдено")
        }

        String displayName = packageJsonContent["displayName"]?.toString()?.trim()
        if (displayName.isEmpty()) {
            return createResult(false, "Поле displayName в package.json не заполнено")
        }

        return createResult(true, "Поле displayName валидно: ${displayName}")
    }

    Map checkDocumentationUrl() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("documentationUrl")) {
            return createResult(false, "Поле documentationUrl в package.json не найдено")
        }

        String documentationUrl = packageJsonContent["documentationUrl"]?.toString()?.trim()
        if (documentationUrl.isEmpty()) {
            return createResult(false, "Поле documentationUrl в package.json не заполнено")
        }

        String repoPath = StringUtils.pathFinder(script.env.JOB_NAME)
        String encodedRepoPath = repoPath.replaceAll('%2F', '/')
        String expectedPrefix = "${GitlabConstants.GITLAB_URL}/${encodedRepoPath}/-/blob/master/"

        if (!documentationUrl.startsWith(expectedPrefix)) {
            return createResult(false, "documentationUrl должен начинаться с: ${expectedPrefix}")
        }

        return createResult(true, "Поле documentationUrl валидно")
    }

    Map checkChangelogUrl() {
        ensureInitialized()

        if (!packageJsonContent.containsKey("changelogUrl")) {
            return createResult(false, "Поле changelogUrl в package.json не найдено")
        }

        String changelogUrl = packageJsonContent["changelogUrl"]?.toString()?.trim()
        if (changelogUrl.isEmpty()) {
            return createResult(false, "Поле changelogUrl в package.json не заполнено")
        }

        String repoPath = StringUtils.pathFinder(script.env.JOB_NAME)
        String encodedRepoPath = repoPath.replaceAll('%2F', '/')
        String expectedUrl = "${GitlabConstants.GITLAB_URL}/${encodedRepoPath}/-/blob/master/CHANGELOG.md"

        if (changelogUrl != expectedUrl) {
            return createResult(false, "changelogUrl должен быть: ${expectedUrl}")
        }

        return createResult(true, "Поле changelogUrl валидно")
    }

    Map checkFileExistence() {
        try {
            this.projectId = gitlab.getProjectIdByJobName()
            def result = fileExistenceService.checkFileExistenceInBranches(
                projectId,
                'package.json',
                script.env.CHANGE_BRANCH,
                script.env.CHANGE_TARGET
            )

            return createResult(result.success, result.log)
        } catch (Exception e) {
            return createResult(false, "Ошибка при проверке существования файла package.json: ${e.message}")
        }
    }
}