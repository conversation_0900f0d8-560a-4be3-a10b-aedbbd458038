package org.kefir.validators

import org.kefir.configs.ValidatorsConstants
import org.kefir.services.GitlabService
import org.kefir.services.FileExistenceService
import org.kefir.utils.ParseUtils
import org.kefir.utils.StringUtils

class InitPyValidator extends BaseValidator {
    private final GitlabService gitlab
    private final FileExistenceService fileExistenceService
    private String projectId
    private String projectName
    private String initPyContent
    private String initPyTargetContent
    private boolean initialized = false

    InitPyValidator(Script script) {
        super(script)
        this.gitlab = new GitlabService(script)
        this.fileExistenceService = new FileExistenceService(script)
    }

    private void ensureInitialized() {
        if (!initialized) {
            try {
                this.projectId = gitlab.getProjectIdByJobName()
                this.projectName = gitlab.getProjectNameByJobName()
                this.initPyContent = gitlab.getFileOutputFromRemote(projectId, '__init__.py', script.env.CHANGE_BRANCH)

                try {
                    this.initPyTargetContent = gitlab.getFileOutputFromRemote(projectId, '__init__.py', script.env.CHANGE_TARGET)
                } catch (Exception targetException) {
                    script.echo("Файл __init__.py не найден в целевой ветке ${script.env.CHANGE_TARGET}")
                    this.initPyTargetContent = null
                }

                initialized = true
            } catch (Exception e) {
                script.error("Ошибка инициализации InitPyValidator: ${e.message}")
                throw e
            }
        }
    }

    Map checkBlInfo() {
        ensureInitialized()
        def parsedBlInfo = ParseUtils.parseBlInfo(initPyContent)
        if (parsedBlInfo == [:]) {
            return createResult(false, "Блок bl_info не найден в __init__.py или он пустой")
        }
        return createResult(true, "Блок bl_info найден")
    }

    Map checkName() {
        ensureInitialized()
        def parsedBlInfo = ParseUtils.parseBlInfo(initPyContent)

        if (!parsedBlInfo.containsKey('name')) {
            return createResult(false, "Поле name не найдено в блоке bl_info")
        }

        String name = parsedBlInfo['name']?.toString()?.trim()
        if (name.isEmpty()) {
            return createResult(false, "Поле name в блоке bl_info не заполнено")
        }

        return createResult(true, "Поле name в блоке bl_info валидно")
    }

    Map checkVersion() {
        ensureInitialized()
        def parsedBlInfo = ParseUtils.parseBlInfo(initPyContent)

        if (!parsedBlInfo.containsKey('version')) {
            return createResult(false, "Поле version не найдено в блоке bl_info")
        }

        String version = parsedBlInfo['version']?.toString()?.trim()

        if (version.isEmpty()) {
            return createResult(false, "Поле version в блоке bl_info не заполнено")
        }

        if (!(version ==~ ValidatorsConstants.INIT_PY_SEM_VER_PATTERN)) {
            return createResult(false, "Значение поля version не соответствует стандарту версионирования (SemVer)")
        }

        if (initPyTargetContent != null) {
            def parsedTargetBlInfo = ParseUtils.parseBlInfo(initPyTargetContent)
            String targetVersion = parsedTargetBlInfo['version']?.toString()?.trim()

            def listVersion = StringUtils.parseVersionString(version)
            def listTargetVersion = StringUtils.parseVersionString(targetVersion)

            if (!StringUtils.isVersionGreater(listVersion, listTargetVersion)) {
                return createResult(false, "Версия ${version} должна быть больше чем ${targetVersion} в ветке ${script.env.CHANGE_TARGET}")
            }
        } else {
            return createResult(true, "Версия ${version} валидна, но файл отсутствует в целевой ветке ${script.env.CHANGE_TARGET} - сравнивать не с чем")
        }

        return createResult(true, "Версия ${version} валидна")
    }

    Map checkAuthor() {
        ensureInitialized()
        def parsedBlInfo = ParseUtils.parseBlInfo(initPyContent)

        if (!parsedBlInfo.containsKey('author')) {
            return createResult(false, "Поле author не найдено в блоке bl_info")
        }

        String author = parsedBlInfo['author']?.toString()?.trim()
        if (author.isEmpty()) {
            return createResult(false, "Поле author в блоке bl_info не заполнено")
        }

        return createResult(true, "Поле author в блоке bl_info валидно")
    }

    Map checkFileExistence() {
        try {
            this.projectId = gitlab.getProjectIdByJobName()
            def result = fileExistenceService.checkFileExistenceInBranches(
                projectId,
                '__init__.py',
                script.env.CHANGE_BRANCH,
                script.env.CHANGE_TARGET
            )

            return createResult(result.success, result.log)
        } catch (Exception e) {
            return createResult(false, "Ошибка при проверке существования файла __init__.py: ${e.message}")
        }
    }
}