package org.kefir.validators

abstract class BaseValidator implements Serializable {
    protected final Script script
    protected final String validatorType

    BaseValidator(Script script) {
        this.script = script
        this.validatorType = this.getClass().simpleName.replace('Validator', '').uncapitalize()
    }

    protected Map createResult(boolean success, String log) {
        return [
                success: success,
                log: log,
                validatorType: this.validatorType
        ]
    }
}