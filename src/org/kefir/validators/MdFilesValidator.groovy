package org.kefir.validators

import org.kefir.services.GitlabService

class MdFilesValidator extends BaseValidator{
    private final GitlabService gitlab
    private String projectId
    private String projectName
    private String mdContent
    private String mdTargetContent
    private boolean initialized = false

    MdFilesValidator(Script script) {
        super(script)
        this.gitlab = new GitlabService(script)
    }

    private void ensureInitialized() {
        if (!initialized) {
            try {
                this.projectId = gitlab.getProjectIdByJobName()
                this.projectName = gitlab.getProjectNameByJobName()
                this.mdContent = gitlab.getFileOutputFromRemote(projectId, 'CHANGELOG.md', script.env.CHANGE_BRANCH)
                this.mdTargetContent = gitlab.getFileOutputFromRemote(projectId, 'CHANGELOG.md', script.env.CHANGE_TARGET)
                initialized = true
            } catch (Exception e) {
                script.error("Ошибка инициализации mdValidator: ${e.message}")
                throw e
            }
        }
    }
}
