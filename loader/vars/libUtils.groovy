String stripGitRefspecPrefixes(String arg) {
    return arg?.replaceFirst('^refs/heads/', '')?.replaceFirst('^origin/', '')
}

org.jenkinsci.plugins.workflow.libs.LibraryStep.LoadedClasses loadDynamic(String loadLibrary = 'devops-shared', Boolean changelog = false) {
    return library([
        identifier: loadLibrary + '@' + (params.SHARED_LIB_BRANCH ? this.stripGitRefspecPrefixes(params.SHARED_LIB_BRANCH) : 'master'),
        changelog: changelog
    ])
}
