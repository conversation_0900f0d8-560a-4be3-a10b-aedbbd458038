stages:
  - release

.release_job:
  stage: release
  image:
    name: registry.gitlab.com/juhani/go-semrel-gitlab:v0.21.1
  variables:
    GSG_TAG_PREFIX: ''
  script:
    - release test-git
    - release next-version --allow-current
  rules:
    - if: $CI_MERGE_REQUEST_ID

release_master:
  extends: .release_job
  after_script:
    - release changelog
    - release commit-and-tag CHANGELOG.md
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      allow_failure: true
